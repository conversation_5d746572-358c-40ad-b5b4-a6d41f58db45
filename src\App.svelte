<script>
  const VERSION = "2.0.0";

  import { onDestroy, onMount, tick } from "svelte";
  import { writable } from "svelte/store";
  
  // Component imports
  import AppFooter from "./components/AppFooter.svelte";
  import ChatPanel from "./components/ChatPanel.svelte";
  import BookmarkDialog from "./components/BookmarkDialog.svelte";
  import WaterfallControls from "./components/WaterfallControls.svelte";
  import AudioPanel from "./components/AudioPanel.svelte";
  import DemodBandwidthPanel from "./components/DemodBandwidthPanel.svelte";
  import TutorialOverlay from "./components/TutorialOverlay.svelte";
  import AudioGateOverlay from "./components/AudioGateOverlay.svelte";
  import DisplayStack from "./components/DisplayStack.svelte";
  import ServerInfo from "./components/ServerInfo.svelte";
  import BandsMenu from "./components/BandsMenu.svelte";
  import serverInfo from './server-info.json';
  
  import { eventBus } from './eventBus';
  
  import {
    init,
    audio,
    waterfall,
    events,
    FFTOffsetToFrequency,
    frequencyToFFTOffset,
    waterfallOffsetToFrequency,
  } from "./lib/backend.js";
  import {
    constructLink,
    parseLink,
    storeInLocalStorage,
  } from "./lib/storage.js";
  
  // Component references
  let frequencyInputComponent;
  let passbandTunerComponent;
  let frequencyMarkerComponent;
  let displayStackComponent;
  let audioPanel;
  let demodBandwidthPanel;
  
  // State
  let frequency = "14074.00";
  let demodulation = "USB";
  let bandwidth = "2.8";
  let link = "";
  let showBookmarkDialog = false;
  let showBandsMenu = false;
  let audioGateVisible = true;
  let showTutorial = false;
  let vfoSwitchNotification = '';
  
  // VFO State - will be initialized with server defaults
  let currentVFO = 'A';
  let vfoA = {
    frequency: null,
    demodulation: null,
    bandwidth: null,
    audioRange: null,
    waterfallRange: null,
    zoom: null
  };
  let vfoB = {
    frequency: null,
    demodulation: null,
    bandwidth: null,
    audioRange: null,
    waterfallRange: null,
    zoom: null
  };
  
  // Reactive audio panel states
  $: NREnabled = audioPanel?.NREnabled || false;
  $: NBEnabled = audioPanel?.NBEnabled || false;
  $: ANEnabled = audioPanel?.ANEnabled || false;
  $: CTCSSSupressEnabled = audioPanel?.CTCSSSupressEnabled || false;
  $: mute = audioPanel?.mute || false;
  
  // Update interval for regular UI updates
  let updateInterval;
  let lastUpdated = 0;
  
  // Audio gate handler
  function handleAudioGateClick() {
    audioGateVisible = false;
    if (audio && audio.enable) {
      audio.enable();
    }
  }
  
  // VFO functions
  function saveCurrentVFO() {
    const vfo = currentVFO === 'A' ? vfoA : vfoB;
    vfo.frequency = frequencyInputComponent?.getFrequency() || parseFloat(frequency) * 1000;
    vfo.demodulation = demodulation;
    vfo.bandwidth = parseFloat(bandwidth);
    vfo.audioRange = audio.getAudioRange();
    vfo.waterfallRange = waterfall.getWaterfallRange();
  }
  
  function loadVFO(vfoName) {
    const vfo = vfoName === 'A' ? vfoA : vfoB;
    
    // Restore frequency
    if (vfo.frequency && frequencyInputComponent) {
      frequencyInputComponent.setFrequency(vfo.frequency);
      handleFrequencyChange({ detail: vfo.frequency });
    }
    
    // Restore demodulation
    if (vfo.demodulation) {
      demodulation = vfo.demodulation;
      if (demodBandwidthPanel) {
        demodBandwidthPanel.SetMode(vfo.demodulation);
      }
    }
    
    // Restore waterfall range
    if (vfo.waterfallRange) {
      waterfall.setWaterfallRange(...vfo.waterfallRange);
    }
  }
  
  function switchVFO() {
    // Save current VFO state
    saveCurrentVFO();
    
    // Switch to other VFO
    currentVFO = currentVFO === 'A' ? 'B' : 'A';
    
    // Load new VFO state
    loadVFO(currentVFO);
    
    // Show notification
    vfoSwitchNotification = `Switched to VFO ${currentVFO}`;
    setTimeout(() => {
      vfoSwitchNotification = '';
    }, 2000);
  }
  
  // Update link
  function updateLink() {
    const linkObj = {
      frequency: frequencyInputComponent?.getFrequency()?.toFixed(0) || 0,
      modulation: demodulation,
    };
    frequency = ((frequencyInputComponent?.getFrequency() || 0) / 1e3).toFixed(2);
    const linkQuery = constructLink(linkObj);
    link = `${location.origin}${location.pathname}?${linkQuery}`;
    storeInLocalStorage(linkObj);
  }
  
  // Regular UI update tick
  function updateTick() {
    // Update signal meter in audio panel
    if (audioPanel) {
      audioPanel.updateSignalMeter();
    }
    
    // Update other user displays
    if (events.getLastModified() > lastUpdated) {
      const myRange = audio.getAudioRange();
      const clients = events.getSignalClients();
      // Don't show our own tuning
      const myId = Object.keys(clients).reduce((a, b) => {
        const aRange = clients[a];
        const bRange = clients[b];
        const aDiff = Math.abs(aRange[1] - myRange[1]);
        const bDiff = Math.abs(bRange[1] - myRange[1]);
        return aDiff < bDiff ? a : b;
      });
      delete clients[myId];
      waterfall.setClients(clients);
      requestAnimationFrame(() => {
        waterfall.updateGraduation();
        waterfall.drawClients();
      });
      lastUpdated = events.getLastModified();
    }
  }
  
  // Handle frequency input display
  function handleWheel(node) {
    function onWheel(event) {
      event.preventDefault();
      const delta = event.deltaY > 0 ? -1 : 1;
      const isShiftPressed = event.shiftKey;
      
      let frequencyHz = Math.round(parseFloat(frequency) * 1e3);
      
      function adjustFrequency(freq, direction, shiftPressed) {
        const step = shiftPressed ? 100 : 50;
        const lastDigits = freq % step;
        
        if (lastDigits === 0) {
          return freq + direction * step;
        } else if (direction > 0) {
          return Math.ceil(freq / step) * step;
        } else {
          return Math.floor(freq / step) * step;
        }
      }
      
      frequencyHz = adjustFrequency(frequencyHz, delta, isShiftPressed);
      frequency = (frequencyHz / 1e3).toFixed(2);
      
      frequencyInputComponent.setFrequency(frequencyHz);
      eventBus.publish('frequencyChange', { detail: frequencyHz });
    }

    node.addEventListener('wheel', onWheel);

    return {
      destroy() {
        node.removeEventListener('wheel', onWheel);
      }
    };
  }
  
  // Event handlers
  function handleFrequencyChange(event) {
    const newFrequency = event.detail;
    const audioRange = audio.getAudioRange();
    
    const [l, m, r] = audioRange.map(FFTOffsetToFrequency);
    
    // Preserve current bandwidth settings
    let audioParameters = [
      newFrequency - (m - l),
      newFrequency,
      newFrequency + (r - m),
    ].map(frequencyToFFTOffset);
    const newm = audioParameters[1];

    const lOffset = newFrequency - (m - l) - 200;
    const mOffset = newFrequency - 750;
    const rOffset = newFrequency + (r - m) - 200;

    const audioParametersOffset = [lOffset, mOffset, rOffset].map(frequencyToFFTOffset);

    // If the ranges are not within limit, shift it back
    let [waterfallL, waterfallR] = waterfall.getWaterfallRange();
    if (newm < waterfallL || newm >= waterfallR) {
      const limits = Math.floor((waterfallR - waterfallL) / 2);
      let offset;
      if (audioRange[1] >= waterfallL && audioRange[1] < waterfallR) {
        offset = audioRange[1] - waterfallL;
      } else {
        offset = limits;
      }
      const newMid = Math.min(
        waterfall.waterfallMaxSize - limits,
        Math.max(limits, newm - offset + limits),
      );

      waterfallL = Math.floor(newMid - limits);
      waterfallR = Math.floor(newMid + limits);
      waterfall.setWaterfallRange(waterfallL, waterfallR);
    }
    audio.setAudioRange(...audioParameters, ...audioParametersOffset);
    displayStackComponent.updatePassband();
    updateLink();
    if (!event.markerclick) {
      waterfall.checkBandAndSetMode && waterfall.checkBandAndSetMode(newFrequency);
    }
    frequencyMarkerComponent.updateFrequencyMarkerPositions();
  }
  
  function handleWaterfallMagnify(e, type) {
    let [l, m, r] = audio.getAudioRange();
    const [waterfallL, waterfallR] = waterfall.getWaterfallRange();
    const offset = ((m - waterfallL) / (waterfallR - waterfallL)) * waterfall.canvasWidth;
    
    switch (type) {
      case "max":
        m = Math.min(waterfall.waterfallMaxSize - 512, Math.max(512, m));
        l = Math.floor(m - 512);
        r = Math.ceil(m + 512);
        break;
      case "+":
        e.coords = { x: offset };
        e.scale = -1;
        waterfall.canvasWheel(e);
        displayStackComponent.updatePassband();
        frequencyMarkerComponent.updateFrequencyMarkerPositions();
        return;
      case "-":
        e.coords = { x: offset };
        e.scale = 1;
        waterfall.canvasWheel(e);
        displayStackComponent.updatePassband();
        frequencyMarkerComponent.updateFrequencyMarkerPositions();
        return;
      case "min":
        l = 0;
        r = waterfall.waterfallMaxSize;
        break;
    }
    waterfall.setWaterfallRange(l, r);
    frequencyMarkerComponent.updateFrequencyMarkerPositions();
    displayStackComponent.updatePassband();
  }
  
  // S-meter drawing
  function drawSMeter(activeSegments) {
    const canvas = document.getElementById("sMeter");
    if (!canvas || !(canvas instanceof HTMLCanvasElement)) return;
    
    const ctx = canvas.getContext("2d");
    canvas.width = 300;
    canvas.height = 40;

    const width = canvas.width;
    const height = canvas.height;

    ctx.clearRect(0, 0, width, height);

    const segmentWidth = 6;
    const segmentGap = 3;
    const segmentHeight = 8;
    const lineY = 15;
    const labelY = 25;
    const tickHeight = 5;
    const longTickHeight = 5;

    const s9Position = width / 2;

    ctx.strokeStyle = "#a7e6fe";
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.moveTo(0, lineY);
    ctx.lineTo(s9Position, lineY);
    ctx.stroke();

    ctx.strokeStyle = "#ed1c24";
    ctx.beginPath();
    ctx.moveTo(s9Position, lineY);
    ctx.lineTo(268, lineY);
    ctx.stroke();

    for (let i = 0; i < 30; i++) {
      const x = i * (segmentWidth + segmentGap);
      if (i < activeSegments) {
        ctx.fillStyle = i < 17 ? "#a3eced" : "#d9191c";
      } else {
        ctx.fillStyle = i < 17 ? "#003333" : "#330000";
      }
      ctx.fillRect(x, 0, segmentWidth, segmentHeight);
    }

    ctx.font = "11px monospace";
    ctx.textAlign = "center";

    const labels = ["S1", "3", "5", "7", "9", "+20", "+40", "+60dB"];

    for (let i = 0; i <= 16; i++) {
      const x = i * 16.6970588235;
      ctx.fillStyle = x <= s9Position ? "#a3eced" : "#d9191c";

      if (i % 2 === 1) {
        ctx.fillRect(x, lineY, 1, longTickHeight + 2);
        if ((i - 1) / 2 < labels.length) {
          ctx.fillText(labels[(i - 1) / 2], x, labelY + 8);
        }
      } else {
        ctx.fillRect(x, lineY, 1, tickHeight);
      }
    }
  }
  
  let backendPromise;
  onMount(async () => {
    // Initialize backend
    backendPromise = init();
    await backendPromise;
    
    // Enable after connection established
    [...document.getElementsByTagName("button"),
     ...document.getElementsByTagName("input")].forEach((element) => {
      element.disabled = false;
    });
    
    // Initialize components
    if (demodBandwidthPanel) {
      demodBandwidthPanel.initialize();
    }
    
    frequencyInputComponent.setFrequency(FFTOffsetToFrequency(audio.getAudioRange()[1]));
    frequencyInputComponent.updateFrequencyLimits(
      audio.baseFreq,
      audio.baseFreq + audio.totalBandwidth,
    );
    
    demodulation = audio.settings.defaults.modulation;
    
    // Initialize both VFOs with server defaults
    const defaultFrequency = FFTOffsetToFrequency(audio.getAudioRange()[1]);
    const defaultModulation = audio.settings.defaults.modulation;
    const defaultBandwidth = parseFloat(bandwidth);
    
    // Initialize VFO A and B with same defaults
    vfoA = {
      frequency: defaultFrequency,
      demodulation: defaultModulation,
      bandwidth: defaultBandwidth,
      audioRange: audio.getAudioRange(),
      waterfallRange: waterfall.getWaterfallRange(),
      zoom: null
    };
    
    vfoB = {
      frequency: defaultFrequency,
      demodulation: defaultModulation,
      bandwidth: defaultBandwidth,
      audioRange: audio.getAudioRange(),
      waterfallRange: waterfall.getWaterfallRange(),
      zoom: null
    };
    
    // Update parameters from URL
    const updateParameters = (linkParameters) => {
      frequencyInputComponent.setFrequency(linkParameters.frequency);
      if (frequencyInputComponent.getFrequency() === linkParameters.frequency) {
        handleFrequencyChange({ detail: linkParameters.frequency });
      }
      if (linkParameters.modulation) {
        demodulation = linkParameters.modulation;
        demodBandwidthPanel.SetMode(demodulation);
      }
      frequencyMarkerComponent.updateFrequencyMarkerPositions();
    };
    
    const linkParameters = parseLink(location.search.slice(1));
    updateParameters(linkParameters);
    
    // Initialize audio panel volume after backend is ready
    if (audioPanel) {
      audioPanel.initializeAudio();
    }
    
    // Ensure demodulation is set before passband initialization
    if (demodBandwidthPanel) {
      demodBandwidthPanel.SetMode(demodulation);
    }
    
    // Initial update - wait for next tick to ensure components are ready
    await tick();
    displayStackComponent.updatePassband();
    passbandTunerComponent.updatePassbandLimits();
    updateLink();
    
    // Start update interval
    updateInterval = setInterval(() => requestAnimationFrame(updateTick), 200);
    
    // Set up global references
    window["spectrumAudio"] = audio;
    window["spectrumWaterfall"] = waterfall;
    
    // Check for tutorial
    if (!localStorage.getItem("phantomSDRTutorialCompleted")) {
      await tick();
      showTutorial = true;
    }
    
    // Event bus subscriptions
    eventBus.subscribe('frequencyChange', handleFrequencyChange);
    
    eventBus.subscribe('setMode', (mode) => {
      // Update the demodulation state directly instead of calling SetMode
      demodulation = mode;
      // Call the internal handler to update audio settings with the new mode
      if (demodBandwidthPanel) {
        // We need to wait for the next tick to ensure the prop is updated
        tick().then(() => {
          demodBandwidthPanel.handleDemodulationChange(null, true);
        });
      }
    });
    
    eventBus.subscribe('demodulationChange', (data) => {
      demodulation = data.demodulation;
      frequency = (data.frequency / 1e3).toFixed(2);
      updateLink();
    });
    
    eventBus.subscribe('passbandChange', (data) => {
      const { frequencies, offsets } = data;
      bandwidth = ((frequencies[2] - frequencies[0]) / 1000).toFixed(2);
      frequencyInputComponent.setFrequency(frequencies[1]);
      frequency = (frequencyInputComponent.getFrequency() / 1e3).toFixed(2);
      
      const audioParameters = frequencies.map(frequencyToFFTOffset);
      const audioParametersOffset = offsets.map(frequencyToFFTOffset);
      
      audio.setAudioRange(...audioParameters, ...audioParametersOffset);
      updateLink();
      displayStackComponent.updatePassband();
      waterfall.checkBandAndSetMode && waterfall.checkBandAndSetMode(frequencies[1]);
    });
    
    eventBus.subscribe('frequencyMarkerClick', (data) => {
      handleFrequencyChange({ detail: data.frequency, markerclick: true });
      frequency = (data.frequency / 1e3).toFixed(2);
      frequencyInputComponent.setFrequency(data.frequency);
      demodBandwidthPanel.SetMode(data.modulation);
    });
    
    eventBus.subscribe('passbandUpdate', () => {
      displayStackComponent.updatePassband();
    });
    
    eventBus.subscribe('linkUpdate', updateLink);
    
    // Set middle column width
    const middleColumn = document.getElementById('middle-column');
    function setWidth() {
      document.documentElement.style.setProperty('--middle-column-width', `1372px`);
    }
    setWidth();
    window.addEventListener('resize', setWidth);
    
    return () => {
      window.removeEventListener('resize', setWidth);
    };
  });
  
  onDestroy(() => {
    clearInterval(updateInterval);
    audio.stop();
    waterfall.stop();
  });
</script>

<svelte:window
  on:mousemove={displayStackComponent?.handleWindowMouseMove}
  on:mouseup={displayStackComponent?.handleWindowMouseUp}
  on:keydown={(e) => {
    // Check if frequency input is focused
    const isFrequencyInputFocused = document.activeElement instanceof HTMLInputElement &&
                                    document.activeElement.name === 'frequency';
    
    // Don't process shortcuts if typing in frequency input
    if (isFrequencyInputFocused && !['Enter', 'Escape'].includes(e.key)) {
      return;
    }
    
    // Prevent default for certain keys
    if (['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight', ' ', '+', '-', '='].includes(e.key)) {
      e.preventDefault();
    }
    
    // Switch VFO on 'V' key press
    if (e.key === 'v' || e.key === 'V') {
      switchVFO();
    }
    
    // Frequency control
    if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
      const delta = e.key === 'ArrowUp' ? 1 : -1;
      const step = e.shiftKey ? 100 : 50;
      const currentFreqHz = parseFloat(frequency) * 1e3;
      const newFreqHz = currentFreqHz + (delta * step);
      frequency = (newFreqHz / 1e3).toFixed(2);
      frequencyInputComponent.setFrequency(newFreqHz);
      handleFrequencyChange({ detail: newFreqHz });
    }
    
    if (e.key === 'ArrowLeft' || e.key === 'ArrowRight') {
      const delta = e.key === 'ArrowRight' ? 1 : -1;
      const step = e.shiftKey ? 10000 : 1000; // 10 kHz or 1 kHz
      const currentFreqHz = parseFloat(frequency) * 1e3;
      const newFreqHz = currentFreqHz + (delta * step);
      frequency = (newFreqHz / 1e3).toFixed(2);
      frequencyInputComponent.setFrequency(newFreqHz);
      handleFrequencyChange({ detail: newFreqHz });
    }
    
    // Mode selection
    if (!e.ctrlKey && !e.altKey && !e.metaKey) {
      switch(e.key.toLowerCase()) {
        case 'u':
          demodulation = 'USB';
          demodBandwidthPanel?.SetMode('USB');
          break;
        case 'l':
          demodulation = 'LSB';
          demodBandwidthPanel?.SetMode('LSB');
          break;
        case 'a':
          demodulation = 'AM';
          demodBandwidthPanel?.SetMode('AM');
          break;
        case 'f':
          demodulation = 'FM';
          demodBandwidthPanel?.SetMode('FM');
          break;
        case 'c':
          demodulation = 'CW';
          demodBandwidthPanel?.SetMode('CW');
          break;
      }
    }
    
    // Waterfall zoom controls
    if (e.key === '+' || e.key === '=') {
      handleWaterfallMagnify(e, '+');
    }
    if (e.key === '-') {
      handleWaterfallMagnify(e, '-');
    }
    if (e.key === '0') {
      handleWaterfallMagnify(e, 'max');
    }
    if (e.key === '9') {
      handleWaterfallMagnify(e, 'min');
    }
    
    // Audio controls
    if (e.key === ' ') { // Spacebar for mute
      audioPanel?.handleMuteChange();
    }
    if (e.key === 's' || e.key === 'S') {
      audioPanel?.handleSquelchChange();
    }
    if (e.key === 'r' || e.key === 'R') {
      audioPanel?.handleRecordingChange();
    }
    
    // Other controls
    if (e.key === 'b' || e.key === 'B') {
      showBookmarkDialog = true;
    }
    if (e.key === 'g' || e.key === 'G') {
      showBandsMenu = true;
    }
    if (e.key === 'n' || e.key === 'N') {
      audioPanel?.handleNRChange();
    }
    if (e.key === 't' || e.key === 'T') {
      audioPanel?.handleANChange();
    }
    
    // Show keybinds with '?'
    if (e.key === '?') {
      // We need to trigger the keybinds modal in ServerInfo component
      const keybindsBtn = document.querySelector('[title="Keyboard Shortcuts"]');
      if (keybindsBtn instanceof HTMLElement) {
        keybindsBtn.click();
      }
    }
    
    // ESC is now handled in individual components
  }}
/>

<main class="custom-scrollbar">
  <div class="h-screen overflow-hidden flex flex-col min-h-screen">
    <div
      class="w-full sm:h-screen overflow-y-scroll sm:w-1/2 xl:w-1/3 lg:w-1/4 sm:transition-all sm:ease-linear sm:duration-100"
      style="width:100%;"
    >

 


      <div
        class=" bg-custom-dark/95 text-gray-200"
        style="padding-top: 10px;"
      >

        <div class="max-w-screen-lg mx-auto ">

  
             <!-- Server Information -->
             <div class="flex justify-center w-full">
              <ServerInfo />
            </div>
       
            <TutorialOverlay 
            bind:showTutorial
            onComplete={() => showTutorial = false}
          />


          <div class="flex justify-center w-full">
            <DisplayStack
              bind:this={displayStackComponent}
              bind:frequencyInputComponent
              bind:passbandTunerComponent
              bind:frequencyMarkerComponent
              on:change={handleFrequencyChange}
            />
          </div>
          
          <AudioGateOverlay 
            visible={audioGateVisible}
            onClick={handleAudioGateClick}
          />
          
          <!-- VFO Switch Notification -->
          {#if vfoSwitchNotification}
            <div class="fixed top-20 left-1/2 transform -translate-x-1/2 z-50
                        bg-blue-600 text-white px-6 py-3 rounded-lg shadow-lg
                        transition-opacity duration-300"
                 style="opacity: {vfoSwitchNotification ? '1' : '0'}">
              <div class="flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                </svg>
                <span class="font-medium">{vfoSwitchNotification}</span>
              </div>
            </div>
          {/if}
          
          <!-- Main panels -->
          <div class="flex flex-col xl:flex-row rounded p-5 justify-center rounded" id="middle-column">
            <AudioPanel
              bind:this={audioPanel}
              currentFrequency={parseFloat(frequency) * 1e3}
              on:stateChange={(e) => {
                NREnabled = e.detail.NREnabled;
                NBEnabled = e.detail.NBEnabled;
                ANEnabled = e.detail.ANEnabled;
                CTCSSSupressEnabled = e.detail.CTCSSSupressEnabled;
                mute = e.detail.mute;
              }}
            />
            
            <div class="flex flex-col items-center bg-gray-900 py-3 px-6 border-l-0 border-r-0 border border-gray-700 w-full">
              <div class="bg-black rounded-lg p-8 min-w-80 lg:min-w-0 lg:p-4 mb-2 w-full" id="smeter-tut">
                <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
                  <div class="flex flex-col items-center">
                    <input
                      class="text-4xl h-16 w-48 text-center bg-black text-cyan-300 focus:outline-none focus:ring-2 focus:ring-cyan-500 rounded-lg mb-2"
                      type="text"
                      bind:value={frequency}
                      size="3"
                      name="frequency"
                      on:keydown={(e) => {
                        if (e.key === 'Enter') {
                          const freqHz = parseFloat(frequency) * 1e3;
                          frequencyInputComponent.setFrequency(freqHz);
                          handleFrequencyChange({ detail: freqHz });
                        }
                      }}
                      use:handleWheel
                    />
                    <div class="flex items-center justify-center text-xs gap-1 flex-nowrap">
                      <span class="text-blue-400 font-bold px-1 bg-blue-400/20 rounded" style="font-size: 10px;">VFO {currentVFO}</span>
                      <span class="text-gray-600">|</span>
                      <span class="text-green-400 font-medium">{demodulation}</span>
                      <span class="text-gray-600">|</span>
                      <span class="text-cyan-400 font-medium">{bandwidth} kHz</span>
                    </div>
                  </div>
                  
                  <div class="flex flex-col items-center">
                    <div class="flex space-x-1 mb-1">
                      {#each [
                        { label: 'MUTED', enabled: mute, color: 'red' },
                        { label: 'NR', enabled: NREnabled, color: 'green' },
                        { label: 'NB', enabled: NBEnabled, color: 'green' },
                        { label: 'AN', enabled: ANEnabled, color: 'green' }
                      ] as indicator}
                        <div class="px-1 py-0.5 flex items-center justify-center w-12 h-5 relative overflow-hidden">
                          <span class={`text-xs font-mono ${indicator.enabled ? `text-${indicator.color}-500` : `text-${indicator.color}-500 opacity-20`} relative z-10`}>
                            {indicator.label}
                          </span>
                        </div>
                      {/each}
                    </div>
                    <!-- SMeter -->
                    <canvas id="sMeter" width="250" height="40"></canvas>
                  </div>
                </div>
              </div>
              
              <DemodBandwidthPanel
                bind:this={demodBandwidthPanel}
                {frequencyInputComponent}
                {passbandTunerComponent}
                {demodulation}
                on:modeChange={(e) => {
                  demodulation = e.detail;
                  // Directly call handleDemodulationChange after updating state
                  tick().then(() => {
                    demodBandwidthPanel.handleDemodulationChange(null, true);
                  });
                }}
              />
              
              <!-- Zoom Controls and Misc Options -->
              <div class="w-full grid grid-cols-2 gap-2">
                <!-- Zoom Controls -->
                <div class="bg-gray-800/50 rounded-xl p-3 backdrop-blur-sm border border-gray-700/50 shadow-lg">
                  <div class="flex items-center mb-2">
                    <div class="w-1 h-4 bg-purple-500 rounded-full mr-2"></div>
                    <h3 class="text-sm font-medium text-gray-300">Zoom</h3>
                  </div>
                  <div id="zoom-controls" class="grid grid-cols-2 gap-2">
                    {#each [
                      { action: '+', title: 'Zoom in', text: '+' },
                      { action: '-', title: 'Zoom out', text: '-' },
                      { action: 'max', title: 'Zoom to max', text: 'Max' },
                      { action: 'min', title: 'Zoom to min', text: 'Min' }
                    ] as { action, title, text }}
                      <button
                        class="py-2 px-3 rounded-lg font-medium text-sm transition-all duration-200 transform hover:scale-105
                               bg-gray-700/70 text-gray-300 hover:bg-gray-600 hover:text-white border border-gray-600/50"
                        on:click={(e) => handleWaterfallMagnify(e, action)}
                        {title}
                      >
                        {text}
                      </button>
                    {/each}
                  </div>
                </div>
                
                <!-- Processing Options -->
                <div class="bg-gray-800/50 rounded-xl p-3 backdrop-blur-sm border border-gray-700/50 shadow-lg">
                  <div class="flex items-center mb-2">
                    <div class="w-1 h-4 bg-orange-500 rounded-full mr-2"></div>
                    <h3 class="text-sm font-medium text-gray-300">Processing</h3>
                  </div>
                  <div id="moreoptions" class="grid grid-cols-2 gap-2">
                    <button
                      class="relative py-2 px-3 rounded-lg font-medium text-sm transition-all duration-200 transform hover:scale-105
                             {NREnabled ? 'bg-green-600 text-white shadow-lg shadow-green-600/30 hover:bg-green-700 ring-2 ring-green-400' :
                                      'bg-gray-700/70 text-gray-300 hover:bg-gray-600 hover:text-white border border-gray-600/50'}"
                      on:click={() => audioPanel?.handleNRChange()}
                      title="Noise Reduction"
                    >
                      {#if NREnabled}
                        <div class="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      {/if}
                      NR
                    </button>
                    <button
                      class="relative py-2 px-3 rounded-lg font-medium text-sm transition-all duration-200 transform hover:scale-105
                             {NBEnabled ? 'bg-green-600 text-white shadow-lg shadow-green-600/30 hover:bg-green-700 ring-2 ring-green-400' :
                                      'bg-gray-700/70 text-gray-300 hover:bg-gray-600 hover:text-white border border-gray-600/50'}"
                      on:click={() => audioPanel?.handleNBChange()}
                      title="Noise Blanker"
                    >
                      {#if NBEnabled}
                        <div class="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      {/if}
                      NB
                    </button>
                    <button
                      class="relative py-2 px-3 rounded-lg font-medium text-sm transition-all duration-200 transform hover:scale-105
                             {ANEnabled ? 'bg-green-600 text-white shadow-lg shadow-green-600/30 hover:bg-green-700 ring-2 ring-green-400' :
                                      'bg-gray-700/70 text-gray-300 hover:bg-gray-600 hover:text-white border border-gray-600/50'}"
                      on:click={() => audioPanel?.handleANChange()}
                      title="Auto Notch"
                    >
                      {#if ANEnabled}
                        <div class="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      {/if}
                      AN
                    </button>
                    <button
                      class="relative py-2 px-3 rounded-lg font-medium text-sm transition-all duration-200 transform hover:scale-105
                             {CTCSSSupressEnabled ? 'bg-green-600 text-white shadow-lg shadow-green-600/30 hover:bg-green-700 ring-2 ring-green-400' :
                                      'bg-gray-700/70 text-gray-300 hover:bg-gray-600 hover:text-white border border-gray-600/50'}"
                      on:click={() => audioPanel?.handleCTCSSChange()}
                      title="CTCSS Filter"
                    >
                      {#if CTCSSSupressEnabled}
                        <div class="absolute -top-1 -right-1 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                      {/if}
                      CTCSS
                    </button>
                  </div>
                </div>
              </div>
            </div>
            
            <WaterfallControls>
              <div slot="extra-controls" class="w-full space-y-3">
                <!-- Bands and Bookmarks Row -->
                <div class="grid grid-cols-2 gap-3">
                  <!-- Bands Button -->
                  <button
                    id="bands-button"
                    class="bg-gray-800/50 rounded-xl p-4 backdrop-blur-sm border border-gray-700/50 shadow-lg
                           hover:bg-gray-700/70 hover:border-gray-600 transition-all duration-200 transform hover:scale-[1.02]
                           flex items-center justify-center gap-2 group"
                    on:click={() => showBandsMenu = true}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg"
                         class="h-5 w-5 text-green-400 group-hover:text-green-300 transition-colors"
                         viewBox="0 0 20 20"
                         fill="currentColor">
                      <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                      <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 1 1 0 000 2H6a2 2 0 00-2 2v6a2 2 0 002 2h2a1 1 0 100 2H6a4 4 0 01-4-4V5z" clip-rule="evenodd" />
                      <path fill-rule="evenodd" d="M16 5a2 2 0 00-2-2 1 1 0 100 2h0a2 2 0 012 2v6a2 2 0 01-2 2h-2a1 1 0 100 2h2a4 4 0 004-4V5z" clip-rule="evenodd" />
                    </svg>
                    <span class="text-gray-300 font-medium group-hover:text-white transition-colors text-sm">
                      Bands
                    </span>
                  </button>
                  
                  <!-- Bookmarks Button -->
                  <button
                    id="bookmark-button"
                    class="bg-gray-800/50 rounded-xl p-4 backdrop-blur-sm border border-gray-700/50 shadow-lg
                           hover:bg-gray-700/70 hover:border-gray-600 transition-all duration-200 transform hover:scale-[1.02]
                           flex items-center justify-center gap-2 group"
                    on:click={() => showBookmarkDialog = true}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg"
                         class="h-5 w-5 text-yellow-400 group-hover:text-yellow-300 transition-colors"
                         viewBox="0 0 20 20"
                         fill="currentColor">
                      <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                    </svg>
                    <span class="text-gray-300 font-medium group-hover:text-white transition-colors text-sm">
                      Bookmarks
                    </span>
                  </button>
                </div>
                
                <!-- User Count -->
                <div id="user_count_container" class="w-full bg-gray-800/50 rounded-xl py-2 px-4 backdrop-blur-sm border border-gray-700/50 shadow-lg">
                  <div id="total_user_count" class="flex items-center justify-between text-sm">
                    <div class="flex items-center gap-2">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                      </svg>
                      <span class="text-gray-400 font-medium">Active Users</span>
                    </div>
                    <div class="w-px h-4 bg-gray-600 mx-3"></div>
                    <!-- Content will be populated by JavaScript but will have a horizontal layout -->
                  </div>
                </div>
              </div>
            </WaterfallControls>
          </div>
          
          <BookmarkDialog
          bind:showDialog={showBookmarkDialog}
          currentFrequency={parseFloat(frequency) * 1e3}
          {demodulation}
          {link}
        />
        
        <BandsMenu bind:showBandsMenu={showBandsMenu} />
          
          {#if serverInfo.chatEnabled}
            <div class="flex flex-col rounded p-2 justify-center" id="chat-column">
              <ChatPanel
                currentFrequency={parseFloat(frequency) * 1e3}
                {demodulation}
              />
            </div>
          {/if}
          
        </div>

      </div>
      <AppFooter version={VERSION} />
    </div>

  </div>
</main>

<svelte:head>
  <link href="https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap" rel="stylesheet">
  <link href="https://fonts.googleapis.com/css2?family=Courier+Prime:ital,wght@0,400;0,700;1,400;1,700&display=swap" rel="stylesheet">
</svelte:head>

<style global lang="postcss">
  body {
    font-family: 'Inter', sans-serif;
    background-color: #f0f0f0;
    color: #333;
    line-height: 1.6;
    margin: 0;
    padding: 0;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  #hero {
    background-color: #2c3e50;
    color: #ecf0f1;
    padding: 100px 0;
    text-align: center;
  }

  #tagline {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .btn {
    display: inline-block;
    padding: 12px 24px;
    background-color: #e74c3c;
    color: #fff;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 700;
    transition: background-color 0.3s ease;
  }

  .btn:hover {
    background-color: #c0392b;
  }

  :root {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen,
      Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  }

  @media (min-width: 1372px) {
    #chat-box {
      min-width: var(--middle-column-width);
    }
    #chat-column {
      align-items: center;
    }
  }

  .full-screen-container {
    display: flex;
    flex-direction: row;
    height: 100vh;
  }

  .side-nav {
    flex-basis: 250px; 
    overflow-y: auto;
    background-color: #333;
    color: #fff;
  }

  .main-content {
    flex-grow: 1;
    overflow-y: auto;
    padding: 20px;
    max-width: 1372px; 
    margin: auto;
  }

  .tab-content {
    display: none; 
  }

  .tab-content.active {
    display: block; 
  }

  :global(body.light-mode) {
    background-color: #a9a9a9;
    transition: background-color 0.3s;
  }
  
  :global(body) {
    background-color: #212121;
    background-image: url('/assets/background.jpg'), url('/assets/background.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-attachment: fixed;
  }

  main {
    text-align: center;
    margin: 0 auto;
  }

  #sMeter {
    width: 300px;
    height: 40px;
    background-color: transparent;
    display: block;
    margin-left: 30px;
    margin-top: 5px;
  }

  .retro-button {
    @apply transform transition-transform;
  }
  
  .retro-button.pressed {
    @apply shadow-none;
  }

  @media screen and (min-width: 1372px) {
    #chat-column {
      align-items: center;
    }
  }
</style>
