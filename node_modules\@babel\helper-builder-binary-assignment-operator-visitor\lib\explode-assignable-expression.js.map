{"version": 3, "names": ["_t", "require", "assignmentExpression", "cloneNode", "isIdentifier", "isLiteral", "isMemberExpression", "isPrivateName", "isPureish", "is<PERSON><PERSON><PERSON>", "memberExpression", "toCom<PERSON><PERSON>ey", "getObjRef", "node", "nodes", "scope", "ref", "hasBinding", "name", "object", "Error", "temp", "generateUidIdentifierBasedOnNode", "push", "id", "getPropRef", "prop", "property", "key", "explode", "obj", "uid", "computed"], "sources": ["../src/explode-assignable-expression.ts"], "sourcesContent": ["import type { Scope } from \"@babel/traverse\";\nimport {\n  assignmentExpression,\n  cloneNode,\n  isIdentifier,\n  isLiteral,\n  isMemberExpression,\n  isPrivateName,\n  isPureish,\n  isSuper,\n  memberExpression,\n  toComputedKey,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nfunction getObjRef(\n  node: t.Identifier | t.MemberExpression,\n  nodes: Array<t.AssignmentExpression>,\n  scope: Scope,\n): t.Identifier | t.Super {\n  let ref;\n  if (isIdentifier(node)) {\n    if (scope.hasBinding(node.name)) {\n      // this variable is declared in scope so we can be 100% sure\n      // that evaluating it multiple times won't trigger a getter\n      // or something else\n      return node;\n    } else {\n      // could possibly trigger a getter so we need to only evaluate\n      // it once\n      ref = node;\n    }\n  } else if (isMemberExpression(node)) {\n    ref = node.object;\n\n    if (isSuper(ref) || (isIdentifier(ref) && scope.hasBinding(ref.name))) {\n      // the object reference that we need to save is locally declared\n      // so as per the previous comment we can be 100% sure evaluating\n      // it multiple times will be safe\n      // Super cannot be directly assigned so lets return it also\n      return ref;\n    }\n  } else {\n    throw new Error(`We can't explode this node type ${node[\"type\"]}`);\n  }\n\n  const temp = scope.generateUidIdentifierBasedOnNode(ref);\n  scope.push({ id: temp });\n  nodes.push(assignmentExpression(\"=\", cloneNode(temp), cloneNode(ref)));\n  return temp;\n}\n\nfunction getPropRef(\n  node: t.MemberExpression,\n  nodes: Array<t.AssignmentExpression>,\n  scope: Scope,\n): t.Identifier | t.Literal {\n  const prop = node.property;\n  if (isPrivateName(prop)) {\n    throw new Error(\n      \"We can't generate property ref for private name, please install `@babel/plugin-transform-class-properties`\",\n    );\n  }\n  const key = toComputedKey(node, prop);\n  if (isLiteral(key) && isPureish(key)) return key;\n\n  const temp = scope.generateUidIdentifierBasedOnNode(prop);\n  scope.push({ id: temp });\n  nodes.push(assignmentExpression(\"=\", cloneNode(temp), cloneNode(prop)));\n  return temp;\n}\n\nexport default function explode(\n  node: t.Identifier | t.MemberExpression,\n  nodes: Array<t.AssignmentExpression>,\n  scope: Scope,\n): {\n  uid: t.Identifier | t.MemberExpression | t.Super;\n  ref: t.Identifier | t.MemberExpression;\n} {\n  const obj = getObjRef(node, nodes, scope);\n\n  let ref, uid;\n\n  if (isIdentifier(node)) {\n    ref = cloneNode(node);\n    uid = obj;\n  } else {\n    const prop = getPropRef(node, nodes, scope);\n    const computed = node.computed || isLiteral(prop);\n    uid = memberExpression(cloneNode(obj), cloneNode(prop), computed);\n    ref = memberExpression(cloneNode(obj), cloneNode(prop), computed);\n  }\n\n  return {\n    uid: uid,\n    ref: ref,\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,EAAA,GAAAC,OAAA;AAWsB;EAVpBC,oBAAoB;EACpBC,SAAS;EACTC,YAAY;EACZC,SAAS;EACTC,kBAAkB;EAClBC,aAAa;EACbC,SAAS;EACTC,OAAO;EACPC,gBAAgB;EAChBC;AAAa,IAAAX,EAAA;AAIf,SAASY,SAASA,CAChBC,IAAuC,EACvCC,KAAoC,EACpCC,KAAY,EACY;EACxB,IAAIC,GAAG;EACP,IAAIZ,YAAY,CAACS,IAAI,CAAC,EAAE;IACtB,IAAIE,KAAK,CAACE,UAAU,CAACJ,IAAI,CAACK,IAAI,CAAC,EAAE;MAI/B,OAAOL,IAAI;IACb,CAAC,MAAM;MAGLG,GAAG,GAAGH,IAAI;IACZ;EACF,CAAC,MAAM,IAAIP,kBAAkB,CAACO,IAAI,CAAC,EAAE;IACnCG,GAAG,GAAGH,IAAI,CAACM,MAAM;IAEjB,IAAIV,OAAO,CAACO,GAAG,CAAC,IAAKZ,YAAY,CAACY,GAAG,CAAC,IAAID,KAAK,CAACE,UAAU,CAACD,GAAG,CAACE,IAAI,CAAE,EAAE;MAKrE,OAAOF,GAAG;IACZ;EACF,CAAC,MAAM;IACL,MAAM,IAAII,KAAK,CAAE,mCAAkCP,IAAI,CAAC,MAAM,CAAE,EAAC,CAAC;EACpE;EAEA,MAAMQ,IAAI,GAAGN,KAAK,CAACO,gCAAgC,CAACN,GAAG,CAAC;EACxDD,KAAK,CAACQ,IAAI,CAAC;IAAEC,EAAE,EAAEH;EAAK,CAAC,CAAC;EACxBP,KAAK,CAACS,IAAI,CAACrB,oBAAoB,CAAC,GAAG,EAAEC,SAAS,CAACkB,IAAI,CAAC,EAAElB,SAAS,CAACa,GAAG,CAAC,CAAC,CAAC;EACtE,OAAOK,IAAI;AACb;AAEA,SAASI,UAAUA,CACjBZ,IAAwB,EACxBC,KAAoC,EACpCC,KAAY,EACc;EAC1B,MAAMW,IAAI,GAAGb,IAAI,CAACc,QAAQ;EAC1B,IAAIpB,aAAa,CAACmB,IAAI,CAAC,EAAE;IACvB,MAAM,IAAIN,KAAK,CACb,4GACF,CAAC;EACH;EACA,MAAMQ,GAAG,GAAGjB,aAAa,CAACE,IAAI,EAAEa,IAAI,CAAC;EACrC,IAAIrB,SAAS,CAACuB,GAAG,CAAC,IAAIpB,SAAS,CAACoB,GAAG,CAAC,EAAE,OAAOA,GAAG;EAEhD,MAAMP,IAAI,GAAGN,KAAK,CAACO,gCAAgC,CAACI,IAAI,CAAC;EACzDX,KAAK,CAACQ,IAAI,CAAC;IAAEC,EAAE,EAAEH;EAAK,CAAC,CAAC;EACxBP,KAAK,CAACS,IAAI,CAACrB,oBAAoB,CAAC,GAAG,EAAEC,SAAS,CAACkB,IAAI,CAAC,EAAElB,SAAS,CAACuB,IAAI,CAAC,CAAC,CAAC;EACvE,OAAOL,IAAI;AACb;AAEe,SAASQ,OAAOA,CAC7BhB,IAAuC,EACvCC,KAAoC,EACpCC,KAAY,EAIZ;EACA,MAAMe,GAAG,GAAGlB,SAAS,CAACC,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC;EAEzC,IAAIC,GAAG,EAAEe,GAAG;EAEZ,IAAI3B,YAAY,CAACS,IAAI,CAAC,EAAE;IACtBG,GAAG,GAAGb,SAAS,CAACU,IAAI,CAAC;IACrBkB,GAAG,GAAGD,GAAG;EACX,CAAC,MAAM;IACL,MAAMJ,IAAI,GAAGD,UAAU,CAACZ,IAAI,EAAEC,KAAK,EAAEC,KAAK,CAAC;IAC3C,MAAMiB,QAAQ,GAAGnB,IAAI,CAACmB,QAAQ,IAAI3B,SAAS,CAACqB,IAAI,CAAC;IACjDK,GAAG,GAAGrB,gBAAgB,CAACP,SAAS,CAAC2B,GAAG,CAAC,EAAE3B,SAAS,CAACuB,IAAI,CAAC,EAAEM,QAAQ,CAAC;IACjEhB,GAAG,GAAGN,gBAAgB,CAACP,SAAS,CAAC2B,GAAG,CAAC,EAAE3B,SAAS,CAACuB,IAAI,CAAC,EAAEM,QAAQ,CAAC;EACnE;EAEA,OAAO;IACLD,GAAG,EAAEA,GAAG;IACRf,GAAG,EAAEA;EACP,CAAC;AACH"}