{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_core", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "<PERSON><PERSON>", "scope", "getBinding", "rename", "UnaryExpression", "path", "node", "parent", "operator", "parentPath", "isBinaryExpression", "t", "EQUALITY_BINARY_OPERATORS", "indexOf", "opposite", "getOpposite", "isStringLiteral", "value", "isUnderHelper", "findParent", "isFunction", "_path$get", "get", "helper", "addHelper", "isVariableDeclarator", "id", "isFunctionDeclaration", "call", "callExpression", "argument", "arg", "isIdentifier", "hasBinding", "unary", "unaryExpression", "cloneNode", "replaceWith", "conditionalExpression", "binaryExpression", "stringLiteral"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\nimport { types as t } from \"@babel/core\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-typeof-symbol\",\n\n    visitor: {\n      Scope({ scope }) {\n        if (!scope.getBinding(\"Symbol\")) {\n          return;\n        }\n\n        scope.rename(\"Symbol\");\n      },\n\n      UnaryExpression(path) {\n        const { node, parent } = path;\n        if (node.operator !== \"typeof\") return;\n\n        if (\n          path.parentPath.isBinaryExpression() &&\n          t.EQUALITY_BINARY_OPERATORS.indexOf(\n            (parent as t.BinaryExpression).operator,\n          ) >= 0\n        ) {\n          // optimise `typeof foo === \"string\"` since we can determine that they'll never\n          // need to handle symbols\n          const opposite = path.getOpposite();\n          if (\n            opposite.isStringLiteral() &&\n            opposite.node.value !== \"symbol\" &&\n            opposite.node.value !== \"object\"\n          ) {\n            return;\n          }\n        }\n\n        let isUnderHelper = path.findParent(path => {\n          if (path.isFunction()) {\n            return (\n              // @ts-expect-error the access is coupled with the shape of typeof helper\n              path.get(\"body.directives.0\")?.node.value.value ===\n              \"@babel/helpers - typeof\"\n            );\n          }\n        });\n\n        if (isUnderHelper) return;\n\n        const helper = this.addHelper(\"typeof\");\n\n        // TODO: This is needed for backward compatibility with\n        // @babel/helpers <= 7.8.3.\n        // Remove in Babel 8\n        isUnderHelper = path.findParent(path => {\n          return (\n            (path.isVariableDeclarator() && path.node.id === helper) ||\n            (path.isFunctionDeclaration() &&\n              path.node.id &&\n              path.node.id.name === helper.name)\n          );\n        });\n\n        if (isUnderHelper) {\n          return;\n        }\n\n        const call = t.callExpression(helper, [node.argument]);\n        const arg = path.get(\"argument\");\n        if (arg.isIdentifier() && !path.scope.hasBinding(arg.node.name, true)) {\n          const unary = t.unaryExpression(\"typeof\", t.cloneNode(node.argument));\n          path.replaceWith(\n            t.conditionalExpression(\n              t.binaryExpression(\"===\", unary, t.stringLiteral(\"undefined\")),\n              t.stringLiteral(\"undefined\"),\n              call,\n            ),\n          );\n        } else {\n          path.replaceWith(call);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAAyC,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAE1B,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,yBAAyB;IAE/BC,OAAO,EAAE;MACPC,KAAKA,CAAC;QAAEC;MAAM,CAAC,EAAE;QACf,IAAI,CAACA,KAAK,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE;UAC/B;QACF;QAEAD,KAAK,CAACE,MAAM,CAAC,QAAQ,CAAC;MACxB,CAAC;MAEDC,eAAeA,CAACC,IAAI,EAAE;QACpB,MAAM;UAAEC,IAAI;UAAEC;QAAO,CAAC,GAAGF,IAAI;QAC7B,IAAIC,IAAI,CAACE,QAAQ,KAAK,QAAQ,EAAE;QAEhC,IACEH,IAAI,CAACI,UAAU,CAACC,kBAAkB,CAAC,CAAC,IACpCC,WAAC,CAACC,yBAAyB,CAACC,OAAO,CAChCN,MAAM,CAAwBC,QACjC,CAAC,IAAI,CAAC,EACN;UAGA,MAAMM,QAAQ,GAAGT,IAAI,CAACU,WAAW,CAAC,CAAC;UACnC,IACED,QAAQ,CAACE,eAAe,CAAC,CAAC,IAC1BF,QAAQ,CAACR,IAAI,CAACW,KAAK,KAAK,QAAQ,IAChCH,QAAQ,CAACR,IAAI,CAACW,KAAK,KAAK,QAAQ,EAChC;YACA;UACF;QACF;QAEA,IAAIC,aAAa,GAAGb,IAAI,CAACc,UAAU,CAACd,IAAI,IAAI;UAC1C,IAAIA,IAAI,CAACe,UAAU,CAAC,CAAC,EAAE;YAAA,IAAAC,SAAA;YACrB,OAEE,EAAAA,SAAA,GAAAhB,IAAI,CAACiB,GAAG,CAAC,mBAAmB,CAAC,qBAA7BD,SAAA,CAA+Bf,IAAI,CAACW,KAAK,CAACA,KAAK,MAC/C,yBAAyB;UAE7B;QACF,CAAC,CAAC;QAEF,IAAIC,aAAa,EAAE;QAEnB,MAAMK,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,QAAQ,CAAC;QAKvCN,aAAa,GAAGb,IAAI,CAACc,UAAU,CAACd,IAAI,IAAI;UACtC,OACGA,IAAI,CAACoB,oBAAoB,CAAC,CAAC,IAAIpB,IAAI,CAACC,IAAI,CAACoB,EAAE,KAAKH,MAAM,IACtDlB,IAAI,CAACsB,qBAAqB,CAAC,CAAC,IAC3BtB,IAAI,CAACC,IAAI,CAACoB,EAAE,IACZrB,IAAI,CAACC,IAAI,CAACoB,EAAE,CAAC5B,IAAI,KAAKyB,MAAM,CAACzB,IAAK;QAExC,CAAC,CAAC;QAEF,IAAIoB,aAAa,EAAE;UACjB;QACF;QAEA,MAAMU,IAAI,GAAGjB,WAAC,CAACkB,cAAc,CAACN,MAAM,EAAE,CAACjB,IAAI,CAACwB,QAAQ,CAAC,CAAC;QACtD,MAAMC,GAAG,GAAG1B,IAAI,CAACiB,GAAG,CAAC,UAAU,CAAC;QAChC,IAAIS,GAAG,CAACC,YAAY,CAAC,CAAC,IAAI,CAAC3B,IAAI,CAACJ,KAAK,CAACgC,UAAU,CAACF,GAAG,CAACzB,IAAI,CAACR,IAAI,EAAE,IAAI,CAAC,EAAE;UACrE,MAAMoC,KAAK,GAAGvB,WAAC,CAACwB,eAAe,CAAC,QAAQ,EAAExB,WAAC,CAACyB,SAAS,CAAC9B,IAAI,CAACwB,QAAQ,CAAC,CAAC;UACrEzB,IAAI,CAACgC,WAAW,CACd1B,WAAC,CAAC2B,qBAAqB,CACrB3B,WAAC,CAAC4B,gBAAgB,CAAC,KAAK,EAAEL,KAAK,EAAEvB,WAAC,CAAC6B,aAAa,CAAC,WAAW,CAAC,CAAC,EAC9D7B,WAAC,CAAC6B,aAAa,CAAC,WAAW,CAAC,EAC5BZ,IACF,CACF,CAAC;QACH,CAAC,MAAM;UACLvB,IAAI,CAACgC,WAAW,CAACT,IAAI,CAAC;QACxB;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}