{"version": 3, "names": ["_usingCtx", "_disposeSuppressedError", "SuppressedError", "error", "suppressed", "err", "Error", "name", "empty", "stack", "using", "isAwait", "value", "Object", "TypeError", "dispose", "Symbol", "asyncDispose", "for", "push", "v", "d", "a", "e", "u", "bind", "next", "resource", "pop", "disposalResult", "call", "Promise", "resolve", "then"], "sources": ["../../src/helpers/usingCtx.ts"], "sourcesContent": ["/* @minVersion 7.23.9 */\n\ntype Stack = {\n  v: any;\n  d: () => any;\n  a: boolean;\n};\n\nexport default function _usingCtx() {\n  var _disposeSuppressedError =\n      typeof SuppressedError === \"function\"\n        ? // eslint-disable-next-line no-undef\n          SuppressedError\n        : (function (error: Error, suppressed: Error) {\n            var err = new Error() as SuppressedError;\n            err.name = \"SuppressedError\";\n            err.suppressed = suppressed;\n            err.error = error;\n            return err;\n          } as SuppressedErrorConstructor),\n    empty = {},\n    stack: Stack[] = [];\n  function using(isAwait: boolean, value: any) {\n    if (value != null) {\n      if (Object(value) !== value) {\n        throw new TypeError(\n          \"using declarations can only be used with objects, functions, null, or undefined.\",\n        );\n      }\n      // core-js-pure uses Symbol.for for polyfilling well-known symbols\n      if (isAwait) {\n        var dispose =\n          value[Symbol.asyncDispose || Symbol.for(\"Symbol.asyncDispose\")];\n      }\n      if (dispose == null) {\n        dispose = value[Symbol.dispose || Symbol.for(\"Symbol.dispose\")];\n      }\n      if (typeof dispose !== \"function\") {\n        throw new TypeError(`Property [Symbol.dispose] is not a function.`);\n      }\n      stack.push({ v: value, d: dispose, a: isAwait });\n    }\n    return value;\n  }\n  return {\n    // error\n    e: empty,\n    // using\n    u: using.bind(null, false),\n    // await using\n    a: using.bind(null, true),\n    // dispose\n    d: function () {\n      var error = this.e;\n\n      function next(): any {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        while ((resource = stack.pop())) {\n          try {\n            var resource,\n              disposalResult = resource.d.call(resource.v);\n            if (resource.a) {\n              return Promise.resolve(disposalResult).then(next, err);\n            }\n          } catch (e) {\n            return err(e);\n          }\n        }\n        if (error !== empty) throw error;\n      }\n\n      function err(e: Error) {\n        error = error !== empty ? new _disposeSuppressedError(error, e) : e;\n\n        return next();\n      }\n\n      return next();\n    },\n  };\n}\n"], "mappings": ";;;;;;AAQe,SAASA,SAASA,CAAA,EAAG;EAClC,IAAIC,uBAAuB,GACvB,OAAOC,eAAe,KAAK,UAAU,GAEjCA,eAAe,GACd,UAAUC,KAAY,EAAEC,UAAiB,EAAE;MAC1C,IAAIC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAoB;MACxCD,GAAG,CAACE,IAAI,GAAG,iBAAiB;MAC5BF,GAAG,CAACD,UAAU,GAAGA,UAAU;MAC3BC,GAAG,CAACF,KAAK,GAAGA,KAAK;MACjB,OAAOE,GAAG;IACZ,CAAgC;IACtCG,KAAK,GAAG,CAAC,CAAC;IACVC,KAAc,GAAG,EAAE;EACrB,SAASC,KAAKA,CAACC,OAAgB,EAAEC,KAAU,EAAE;IAC3C,IAAIA,KAAK,IAAI,IAAI,EAAE;MACjB,IAAIC,MAAM,CAACD,KAAK,CAAC,KAAKA,KAAK,EAAE;QAC3B,MAAM,IAAIE,SAAS,CACjB,kFACF,CAAC;MACH;MAEA,IAAIH,OAAO,EAAE;QACX,IAAII,OAAO,GACTH,KAAK,CAACI,MAAM,CAACC,YAAY,IAAID,MAAM,CAACE,GAAG,CAAC,qBAAqB,CAAC,CAAC;MACnE;MACA,IAAIH,OAAO,IAAI,IAAI,EAAE;QACnBA,OAAO,GAAGH,KAAK,CAACI,MAAM,CAACD,OAAO,IAAIC,MAAM,CAACE,GAAG,CAAC,gBAAgB,CAAC,CAAC;MACjE;MACA,IAAI,OAAOH,OAAO,KAAK,UAAU,EAAE;QACjC,MAAM,IAAID,SAAS,CAAE,8CAA6C,CAAC;MACrE;MACAL,KAAK,CAACU,IAAI,CAAC;QAAEC,CAAC,EAAER,KAAK;QAAES,CAAC,EAAEN,OAAO;QAAEO,CAAC,EAAEX;MAAQ,CAAC,CAAC;IAClD;IACA,OAAOC,KAAK;EACd;EACA,OAAO;IAELW,CAAC,EAAEf,KAAK;IAERgB,CAAC,EAAEd,KAAK,CAACe,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC;IAE1BH,CAAC,EAAEZ,KAAK,CAACe,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC;IAEzBJ,CAAC,EAAE,SAAAA,CAAA,EAAY;MACb,IAAIlB,KAAK,GAAG,IAAI,CAACoB,CAAC;MAElB,SAASG,IAAIA,CAAA,EAAQ;QAEnB,OAAQC,QAAQ,GAAGlB,KAAK,CAACmB,GAAG,CAAC,CAAC,EAAG;UAC/B,IAAI;YACF,IAAID,QAAQ;cACVE,cAAc,GAAGF,QAAQ,CAACN,CAAC,CAACS,IAAI,CAACH,QAAQ,CAACP,CAAC,CAAC;YAC9C,IAAIO,QAAQ,CAACL,CAAC,EAAE;cACd,OAAOS,OAAO,CAACC,OAAO,CAACH,cAAc,CAAC,CAACI,IAAI,CAACP,IAAI,EAAErB,GAAG,CAAC;YACxD;UACF,CAAC,CAAC,OAAOkB,CAAC,EAAE;YACV,OAAOlB,GAAG,CAACkB,CAAC,CAAC;UACf;QACF;QACA,IAAIpB,KAAK,KAAKK,KAAK,EAAE,MAAML,KAAK;MAClC;MAEA,SAASE,GAAGA,CAACkB,CAAQ,EAAE;QACrBpB,KAAK,GAAGA,KAAK,KAAKK,KAAK,GAAG,IAAIP,uBAAuB,CAACE,KAAK,EAAEoB,CAAC,CAAC,GAAGA,CAAC;QAEnE,OAAOG,IAAI,CAAC,CAAC;MACf;MAEA,OAAOA,IAAI,CAAC,CAAC;IACf;EACF,CAAC;AACH", "ignoreList": []}