{"version": 3, "names": ["_core", "require", "_helperReplaceSupers", "_helperSplitExportDeclaration", "_helperSkipTransparentExpressionWrappers", "_fields", "_misc", "incrementId", "id", "idx", "length", "unshift", "current", "createPrivateUidGeneratorForClass", "classPath", "currentPrivateId", "privateNames", "Set", "traverse", "PrivateName", "path", "add", "node", "name", "reifiedId", "String", "fromCharCode", "has", "t", "privateName", "identifier", "createLazyPrivateUidGeneratorForClass", "generator", "replaceClassWithVar", "className", "scope", "type", "varId", "generateUidIdentifierBasedOnNode", "classId", "rename", "get", "replaceWith", "cloneNode", "generateLetUidIdentifier", "parent", "newClassExpr", "classExpression", "superClass", "body", "newPath", "sequenceExpression", "generateClassProperty", "key", "value", "isStatic", "classPrivateProperty", "undefined", "classProperty", "addProxyAccessorsFor", "element", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isComputed", "version", "thisArg", "thisExpression", "getterBody", "blockStatement", "returnStatement", "memberExpression", "setterBody", "expressionStatement", "assignmentExpression", "getter", "setter", "classPrivateMethod", "classMethod", "insertAfter", "extractProxyAccessorsFor", "template", "expression", "ast", "getComputedKeyLastElement", "skipTransparentExprWrappers", "isSequenceExpression", "expressions", "getComputedKeyMemoiser", "isConstantExpression", "isIdentifier", "hasUid", "isAssignmentExpression", "left", "Error", "toString", "prependExpressionsToComputedKey", "fieldPath", "push", "maybeSequenceExpression", "appendExpressionsToComputedKey", "completion", "scopeParent", "maybeAssignment", "memoiseComputedKey", "generateUid", "expressionSequence", "completionParent", "parentPath", "pushContainer", "prependExpressionsToFieldInitializer", "initializer", "unaryExpression", "prependExpressionsToStaticBlock", "blockPath", "unshiftContainer", "prependExpressionsToConstructor", "constructorPath", "isProtoInitCallExpression", "protoInitCall", "isCallExpression", "callee", "optimizeSuperCallAndExpressions", "protoInitLocal", "mergedSuperCall", "callExpression", "splice", "isThisExpression", "insertExpressionsAfterSuperCallAndOptimize", "CallExpression", "exit", "is<PERSON><PERSON><PERSON>", "newNodes", "map", "expr", "isCompletionRecord", "skip", "ClassMethod", "kind", "createConstructorFromExpressions", "isDerivedClass", "super", "spreadElement", "restElement", "createStaticBlockFromExpressions", "staticBlock", "FIELD", "ACCESSOR", "METHOD", "GETTER", "SETTER", "STATIC_OLD_VERSION", "STATIC", "DECORATORS_HAVE_THIS", "getElementKind", "toSortedDecoratorInfo", "info", "filter", "el", "generateDecorationList", "decorators", "decoratorsThis", "decsCount", "haveOneThis", "some", "Boolean", "decs", "i", "numericLiteral", "haveThis", "generateDecorationExprs", "decorationInfo", "arrayExpression", "flag", "decoratorsHaveThis", "decoratorsArray", "privateMethods", "extractElementLocalAssignments", "localIds", "locals", "Array", "isArray", "addCallAccessorsFor", "getId", "setId", "movePrivateAccessor", "methodLocalVar", "params", "block", "isClassDecoratableElementPath", "staticBlockToIIFE", "arrowFunctionExpression", "staticBlockToFunctionClosure", "functionExpression", "fieldInitializerToClosure", "exprs", "createFunctionExpressionFromPrivateMethod", "isGenerator", "async", "isAsync", "createSetFunctionNameCall", "state", "addHelper", "createToPropertyKeyCall", "propertyKey", "createPrivateBrandCheckClosure", "brandName", "binaryExpression", "usesFunctionContextOrYieldAwait", "traverseFast", "isYieldExpression", "isAwaitExpression", "isMetaProperty", "meta", "_unused", "usesPrivateField", "isPrivateName", "_unused2", "convertToComputedKey", "computed", "stringLiteral", "hasInstancePrivateAccess", "containsInstancePrivateAccess", "privateNameVisitor", "privateNameVisitorFactory", "privateNamesMap", "stop", "Map", "set", "checkPrivateMethodUpdateError", "decoratedPrivateMethods", "parentParentPath", "buildCodeFrameError", "transformClass", "constant<PERSON>uper", "ignoreFunctionLength", "propertyVisitor", "_classDecorationsId", "classDecorators", "hasElementDecorators", "hasComputedKeysSideEffects", "elemDecsUseFnContext", "generateClassPrivateUid", "classAssignments", "memoiseExpression", "hint", "assignments", "localEvaluatedId", "staticInitLocal", "instancePrivateNames", "elementNode", "static", "isDecorated", "ClassProperty", "ClassPrivateProperty", "ClassAccessorProperty", "_staticInitLocal", "_protoInitLocal", "newId", "newField", "keyP<PERSON>", "elementDecoratorInfo", "classInitLocal", "classIdLocal", "decoratorReceiverId", "handleDecoratorExpressions", "hasSideEffects", "usesFnContext", "object", "isMemberExpression", "_decoratorReceiverId", "willExtractSomeElemDecs", "needsDeclaraionForClassBinding", "classDecorationsFlag", "classDecorations", "classDecorationsId", "computedKeyAssignments", "isClassDeclaration", "decoratorExpressions", "classDecsUsePrivateName", "isClassProperty", "generateUidIdentifier", "lastInstancePrivateName", "needsInstancePrivateBrandCheck", "fieldInitializerExpressions", "staticFieldInitializerExpressions", "isStaticBlock", "hasDecorators", "d", "isPrivate", "isClassPrivateProperty", "isClassMethod", "nameExpr", "newFieldInitId", "newValue", "initId", "valuePath", "args", "callId", "replaceSupers", "ReplaceSupers", "methodPath", "objectRef", "superRef", "file", "refToPreserve", "replace", "remove", "getNextSibling", "initExtraId", "initExtraCall", "elements", "lastComputedElement", "sortedElementDecoratorInfo", "elementDecorations", "elementLocals", "classLocals", "classInitInjected", "classInitCall", "originalClassPath", "originalClass", "staticClosures", "statics", "for<PERSON>ach", "staticBlockClosureId", "fieldValueClosureId", "isClassPrivateMethod", "privateMethodDelegateId", "p", "isRestElement", "staticsClass", "toExpression", "constructorBody", "newExpr", "newExpression", "arguments", "maybeGenerateMemoised", "applyDecoratorWrapper", "applyDecsBody", "firstPublicElement", "createLocalsAssignment", "insertBefore", "variableDeclaration", "variableDeclarator", "size", "crawl", "maybePrivateBrandName", "setClassName", "lhs", "rhs", "availableHelper", "arrayPattern", "objectPattern", "objectProperty", "isProtoKey", "shouldTransformElement", "shouldTransformClass", "NamedEvaluationVisitoryFactory", "isAnonymous", "visitor", "handleComputedProperty", "propertyPath", "keyValue", "ref", "VariableDeclarator", "AssignmentExpression", "operator", "AssignmentPattern", "ObjectExpression", "isDecoratedAnonymousClassExpression", "isClassExpression", "_default", "assertVersion", "assumption", "loose", "inherits", "_assumption", "_assumption2", "VISITED", "WeakSet", "namedEvaluationVisitor", "visitClass", "_className", "_node$id", "Object", "assign", "ExportDefaultDeclaration", "declaration", "updatedVarDeclarationPath", "splitExportDeclaration", "ExportNamedDeclaration", "Class"], "sources": ["../src/decorators.ts"], "sourcesContent": ["import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Visitor } from \"@babel/traverse\";\nimport { types as t, template } from \"@babel/core\";\nimport ReplaceSupers from \"@babel/helper-replace-supers\";\nimport splitExportDeclaration from \"@babel/helper-split-export-declaration\";\nimport * as charCodes from \"charcodes\";\nimport type { PluginAPI, PluginObject, PluginPass } from \"@babel/core\";\nimport { skipTransparentExprWrappers } from \"@babel/helper-skip-transparent-expression-wrappers\";\nimport {\n  privateNameVisitorFactory,\n  type PrivateNameVisitorState,\n} from \"./fields.ts\";\nimport { memoiseComputedKey } from \"./misc.ts\";\n\ninterface Options {\n  /** @deprecated use `constantSuper` assumption instead. Only supported in 2021-12 version. */\n  loose?: boolean;\n}\n\ntype ClassDecoratableElement =\n  | t.ClassMethod\n  | t.ClassPrivateMethod\n  | t.ClassProperty\n  | t.ClassPrivateProperty\n  | t.ClassAccessorProperty;\n\ntype ClassElement =\n  | ClassDecoratableElement\n  | t.TSDeclareMethod\n  | t.TSIndexSignature\n  | t.StaticBlock;\n\ntype ClassElementCanHaveComputedKeys =\n  | t.ClassMethod\n  | t.ClassProperty\n  | t.ClassAccessorProperty;\n\n// TODO(Babel 8): Only keep 2023-11\nexport type DecoratorVersionKind =\n  | \"2023-11\"\n  | \"2023-05\"\n  | \"2023-01\"\n  | \"2022-03\"\n  | \"2021-12\";\n\nfunction incrementId(id: number[], idx = id.length - 1): void {\n  // If index is -1, id needs an additional character, unshift A\n  if (idx === -1) {\n    id.unshift(charCodes.uppercaseA);\n    return;\n  }\n\n  const current = id[idx];\n\n  if (current === charCodes.uppercaseZ) {\n    // if current is Z, skip to a\n    id[idx] = charCodes.lowercaseA;\n  } else if (current === charCodes.lowercaseZ) {\n    // if current is z, reset to A and carry the 1\n    id[idx] = charCodes.uppercaseA;\n    incrementId(id, idx - 1);\n  } else {\n    // else, increment by one\n    id[idx] = current + 1;\n  }\n}\n\n/**\n * Generates a new private name that is unique to the given class. This can be\n * used to create extra class fields and methods for the implementation, while\n * keeping the length of those names as small as possible. This is important for\n * minification purposes (though private names can generally be minified,\n * transpilations and polyfills cannot yet).\n */\nfunction createPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  const currentPrivateId: number[] = [];\n  const privateNames = new Set<string>();\n\n  classPath.traverse({\n    PrivateName(path) {\n      privateNames.add(path.node.id.name);\n    },\n  });\n\n  return (): t.PrivateName => {\n    let reifiedId;\n    do {\n      incrementId(currentPrivateId);\n      reifiedId = String.fromCharCode(...currentPrivateId);\n    } while (privateNames.has(reifiedId));\n\n    return t.privateName(t.identifier(reifiedId));\n  };\n}\n\n/**\n * Wraps the above generator function so that it's run lazily the first time\n * it's actually required. Several types of decoration do not require this, so it\n * saves iterating the class elements an additional time and allocating the space\n * for the Sets of element names.\n */\nfunction createLazyPrivateUidGeneratorForClass(\n  classPath: NodePath<t.ClassDeclaration | t.ClassExpression>,\n): () => t.PrivateName {\n  let generator: () => t.PrivateName;\n\n  return (): t.PrivateName => {\n    if (!generator) {\n      generator = createPrivateUidGeneratorForClass(classPath);\n    }\n\n    return generator();\n  };\n}\n\n/**\n * Takes a class definition and the desired class name if anonymous and\n * replaces it with an equivalent class declaration (path) which is then\n * assigned to a local variable (id). This allows us to reassign the local variable with the\n * decorated version of the class. The class definition retains its original\n * name so that `toString` is not affected, other references to the class\n * are renamed instead.\n */\nfunction replaceClassWithVar(\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n): {\n  id: t.Identifier;\n  path: NodePath<t.ClassDeclaration | t.ClassExpression>;\n} {\n  const id = path.node.id;\n  const scope = path.scope;\n  if (path.type === \"ClassDeclaration\") {\n    const className = id.name;\n    const varId = scope.generateUidIdentifierBasedOnNode(id);\n    const classId = t.identifier(className);\n\n    scope.rename(className, varId.name);\n\n    path.get(\"id\").replaceWith(classId);\n\n    return { id: t.cloneNode(varId), path };\n  } else {\n    let varId: t.Identifier;\n\n    if (id) {\n      className = id.name;\n      varId = generateLetUidIdentifier(scope.parent, className);\n      scope.rename(className, varId.name);\n    } else {\n      varId = generateLetUidIdentifier(\n        scope.parent,\n        typeof className === \"string\" ? className : \"decorated_class\",\n      );\n    }\n\n    const newClassExpr = t.classExpression(\n      typeof className === \"string\" ? t.identifier(className) : null,\n      path.node.superClass,\n      path.node.body,\n    );\n\n    const [newPath] = path.replaceWith(\n      t.sequenceExpression([newClassExpr, varId]),\n    );\n\n    return {\n      id: t.cloneNode(varId),\n      path: newPath.get(\"expressions.0\") as NodePath<t.ClassExpression>,\n    };\n  }\n}\n\nfunction generateClassProperty(\n  key: t.PrivateName | t.Identifier,\n  value: t.Expression | undefined,\n  isStatic: boolean,\n): t.ClassPrivateProperty | t.ClassProperty {\n  if (key.type === \"PrivateName\") {\n    return t.classPrivateProperty(key, value, undefined, isStatic);\n  } else {\n    return t.classProperty(key, value, undefined, undefined, isStatic);\n  }\n}\n\nfunction addProxyAccessorsFor(\n  className: t.Identifier,\n  element: NodePath<ClassDecoratableElement>,\n  getterKey: t.PrivateName | t.Expression,\n  setterKey: t.PrivateName | t.Expression,\n  targetKey: t.PrivateName,\n  isComputed: boolean,\n  isStatic: boolean,\n  version: DecoratorVersionKind,\n): void {\n  const thisArg =\n    (version === \"2023-11\" ||\n      (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n    isStatic\n      ? className\n      : t.thisExpression();\n\n  const getterBody = t.blockStatement([\n    t.returnStatement(\n      t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n    ),\n  ]);\n\n  const setterBody = t.blockStatement([\n    t.expressionStatement(\n      t.assignmentExpression(\n        \"=\",\n        t.memberExpression(t.cloneNode(thisArg), t.cloneNode(targetKey)),\n        t.identifier(\"v\"),\n      ),\n    ),\n  ]);\n\n  let getter: t.ClassMethod | t.ClassPrivateMethod,\n    setter: t.ClassMethod | t.ClassPrivateMethod;\n\n  if (getterKey.type === \"PrivateName\") {\n    getter = t.classPrivateMethod(\"get\", getterKey, [], getterBody, isStatic);\n    setter = t.classPrivateMethod(\n      \"set\",\n      setterKey as t.PrivateName,\n      [t.identifier(\"v\")],\n      setterBody,\n      isStatic,\n    );\n  } else {\n    getter = t.classMethod(\n      \"get\",\n      getterKey,\n      [],\n      getterBody,\n      isComputed,\n      isStatic,\n    );\n    setter = t.classMethod(\n      \"set\",\n      setterKey as t.Expression,\n      [t.identifier(\"v\")],\n      setterBody,\n      isComputed,\n      isStatic,\n    );\n  }\n\n  element.insertAfter(setter);\n  element.insertAfter(getter);\n}\n\nfunction extractProxyAccessorsFor(\n  targetKey: t.PrivateName,\n  version: DecoratorVersionKind,\n): (t.FunctionExpression | t.ArrowFunctionExpression)[] {\n  if (version !== \"2023-11\" && version !== \"2023-05\" && version !== \"2023-01\") {\n    return [\n      template.expression.ast`\n        function () {\n          return this.${t.cloneNode(targetKey)};\n        }\n      ` as t.FunctionExpression,\n      template.expression.ast`\n        function (value) {\n          this.${t.cloneNode(targetKey)} = value;\n        }\n      ` as t.FunctionExpression,\n    ];\n  }\n  return [\n    template.expression.ast`\n      o => o.${t.cloneNode(targetKey)}\n    ` as t.ArrowFunctionExpression,\n    template.expression.ast`\n      (o, v) => o.${t.cloneNode(targetKey)} = v\n    ` as t.ArrowFunctionExpression,\n  ];\n}\n\n/**\n * Get the last element for the given computed key path.\n *\n * This function unwraps transparent wrappers and gets the last item when\n * the key is a SequenceExpression.\n *\n * @param {NodePath<t.Expression>} path The key of a computed class element\n * @returns {NodePath<t.Expression>} The simple completion result\n */\nfunction getComputedKeyLastElement(\n  path: NodePath<t.Expression>,\n): NodePath<t.Expression> {\n  path = skipTransparentExprWrappers(path);\n  if (path.isSequenceExpression()) {\n    const expressions = path.get(\"expressions\");\n    return getComputedKeyLastElement(expressions[expressions.length - 1]);\n  }\n  return path;\n}\n\n/**\n * Get a memoiser of the computed key path.\n *\n * This function does not mutate AST. If the computed key is not a constant\n * expression, this function must be called after the key has been memoised.\n *\n * @param {NodePath<t.Expression>} path The key of a computed class element.\n * @returns {t.Expression} A clone of key if key is a constant expression,\n * otherwise a memoiser identifier.\n */\nfunction getComputedKeyMemoiser(path: NodePath<t.Expression>): t.Expression {\n  const element = getComputedKeyLastElement(path);\n  if (element.isConstantExpression()) {\n    return t.cloneNode(path.node);\n  } else if (element.isIdentifier() && path.scope.hasUid(element.node.name)) {\n    return t.cloneNode(path.node);\n  } else if (\n    element.isAssignmentExpression() &&\n    element.get(\"left\").isIdentifier()\n  ) {\n    return t.cloneNode(element.node.left as t.Identifier);\n  } else {\n    throw new Error(\n      `Internal Error: the computed key ${path.toString()} has not yet been memoised.`,\n    );\n  }\n}\n\n/**\n * Prepend expressions to the computed key of the given field path.\n *\n * If the computed key is a sequence expression, this function will unwrap\n * the sequence expression for optimal output size.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction prependExpressionsToComputedKey(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n  >,\n) {\n  const key = fieldPath.get(\"key\") as NodePath<t.Expression>;\n  if (key.isSequenceExpression()) {\n    expressions.push(...key.node.expressions);\n  } else {\n    expressions.push(key.node);\n  }\n  key.replaceWith(maybeSequenceExpression(expressions));\n}\n\n/**\n * Append expressions to the computed key of the given field path.\n *\n * If the computed key is a constant expression or uid reference, it\n * will prepend expressions before the comptued key. Otherwise it will\n * memoise the computed key to preserve its completion result.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction appendExpressionsToComputedKey(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassMethod | t.ClassProperty | t.ClassAccessorProperty\n  >,\n) {\n  const key = fieldPath.get(\"key\") as NodePath<t.Expression>;\n  const completion = getComputedKeyLastElement(key);\n  if (completion.isConstantExpression()) {\n    prependExpressionsToComputedKey(expressions, fieldPath);\n  } else {\n    const scopeParent = key.scope.parent;\n    const maybeAssignment = memoiseComputedKey(\n      completion.node,\n      scopeParent,\n      scopeParent.generateUid(\"computedKey\"),\n    );\n    if (!maybeAssignment) {\n      // If the memoiseComputedKey returns undefined, the key is already a uid reference,\n      // treat it as a constant expression and prepend expressions before it\n      prependExpressionsToComputedKey(expressions, fieldPath);\n    } else {\n      const expressionSequence = [\n        ...expressions,\n        // preserve the completion result\n        t.cloneNode(maybeAssignment.left),\n      ];\n      const completionParent = completion.parentPath;\n      if (completionParent.isSequenceExpression()) {\n        completionParent.pushContainer(\"expressions\", expressionSequence);\n      } else {\n        completion.replaceWith(\n          maybeSequenceExpression([\n            t.cloneNode(maybeAssignment),\n            ...expressionSequence,\n          ]),\n        );\n      }\n    }\n  }\n}\n\n/**\n * Prepend expressions to the field initializer. If the initializer is not defined,\n * this function will wrap the last expression within a `void` unary expression.\n *\n * @param {t.Expression[]} expressions\n * @param {(NodePath<\n *     t.ClassProperty | t.ClassPrivateProperty | t.ClassAccessorProperty\n *   >)} fieldPath\n */\nfunction prependExpressionsToFieldInitializer(\n  expressions: t.Expression[],\n  fieldPath: NodePath<\n    t.ClassProperty | t.ClassPrivateProperty | t.ClassAccessorProperty\n  >,\n) {\n  const initializer = fieldPath.get(\"value\");\n  if (initializer.node) {\n    expressions.push(initializer.node);\n  } else if (expressions.length > 0) {\n    expressions[expressions.length - 1] = t.unaryExpression(\n      \"void\",\n      expressions[expressions.length - 1],\n    );\n  }\n  initializer.replaceWith(maybeSequenceExpression(expressions));\n}\n\nfunction prependExpressionsToStaticBlock(\n  expressions: t.Expression[],\n  blockPath: NodePath<t.StaticBlock>,\n) {\n  blockPath.unshiftContainer(\n    \"body\",\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  );\n}\n\nfunction prependExpressionsToConstructor(\n  expressions: t.Expression[],\n  constructorPath: NodePath<t.ClassMethod>,\n) {\n  constructorPath.node.body.body.unshift(\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  );\n}\n\nfunction isProtoInitCallExpression(\n  expression: t.Expression,\n  protoInitCall: t.Identifier,\n) {\n  return (\n    t.isCallExpression(expression) &&\n    t.isIdentifier(expression.callee, { name: protoInitCall.name })\n  );\n}\n\n/**\n * Optimize super call and its following expressions\n *\n * @param {t.Expression[]} expressions Mutated by this function. The first element must by a super call\n * @param {t.Identifier} protoInitLocal The generated protoInit id\n * @returns optimized expression\n */\nfunction optimizeSuperCallAndExpressions(\n  expressions: t.Expression[],\n  protoInitLocal: t.Identifier,\n) {\n  // Merge `super(), protoInit(this)` into `protoInit(super())`\n  if (\n    expressions.length >= 2 &&\n    isProtoInitCallExpression(expressions[1], protoInitLocal)\n  ) {\n    const mergedSuperCall = t.callExpression(t.cloneNode(protoInitLocal), [\n      expressions[0],\n    ]);\n    expressions.splice(0, 2, mergedSuperCall);\n  }\n  // Merge `protoInit(super()), this` into `protoInit(super())`\n  if (\n    expressions.length >= 2 &&\n    t.isThisExpression(expressions[expressions.length - 1]) &&\n    isProtoInitCallExpression(\n      expressions[expressions.length - 2],\n      protoInitLocal,\n    )\n  ) {\n    expressions.splice(expressions.length - 1, 1);\n  }\n  return maybeSequenceExpression(expressions);\n}\n\n/**\n * Insert expressions immediately after super() and optimize the output if possible.\n * This function will preserve the completion result using the trailing this expression.\n *\n * @param {t.Expression[]} expressions\n * @param {NodePath<t.ClassMethod>} constructorPath\n * @param {t.Identifier} protoInitLocal The generated protoInit id\n * @returns\n */\nfunction insertExpressionsAfterSuperCallAndOptimize(\n  expressions: t.Expression[],\n  constructorPath: NodePath<t.ClassMethod>,\n  protoInitLocal: t.Identifier,\n) {\n  constructorPath.traverse({\n    CallExpression: {\n      exit(path) {\n        if (!path.get(\"callee\").isSuper()) return;\n        const newNodes = [\n          path.node,\n          ...expressions.map(expr => t.cloneNode(expr)),\n        ];\n        // preserve completion result if super() is in an RHS or a return statement\n        if (path.isCompletionRecord()) {\n          newNodes.push(t.thisExpression());\n        }\n        path.replaceWith(\n          optimizeSuperCallAndExpressions(newNodes, protoInitLocal),\n        );\n\n        path.skip();\n      },\n    },\n    ClassMethod(path) {\n      if (path.node.kind === \"constructor\") {\n        path.skip();\n      }\n    },\n  });\n}\n\n/**\n * Build a class constructor node from the given expressions. If the class is\n * derived, the constructor will call super() first to ensure that `this`\n * in the expressions work as expected.\n *\n * @param {t.Expression[]} expressions\n * @param {boolean} isDerivedClass\n * @returns The class constructor node\n */\nfunction createConstructorFromExpressions(\n  expressions: t.Expression[],\n  isDerivedClass: boolean,\n) {\n  const body: t.Statement[] = [\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  ];\n  if (isDerivedClass) {\n    body.unshift(\n      t.expressionStatement(\n        t.callExpression(t.super(), [t.spreadElement(t.identifier(\"args\"))]),\n      ),\n    );\n  }\n  return t.classMethod(\n    \"constructor\",\n    t.identifier(\"constructor\"),\n    isDerivedClass ? [t.restElement(t.identifier(\"args\"))] : [],\n    t.blockStatement(body),\n  );\n}\n\nfunction createStaticBlockFromExpressions(expressions: t.Expression[]) {\n  return t.staticBlock([\n    t.expressionStatement(maybeSequenceExpression(expressions)),\n  ]);\n}\n\n// 3 bits reserved to this (0-7)\nconst FIELD = 0;\nconst ACCESSOR = 1;\nconst METHOD = 2;\nconst GETTER = 3;\nconst SETTER = 4;\n\nconst STATIC_OLD_VERSION = 5; // Before 2023-05\nconst STATIC = 8; // 1 << 3\nconst DECORATORS_HAVE_THIS = 16; // 1 << 4\n\nfunction getElementKind(element: NodePath<ClassDecoratableElement>): number {\n  switch (element.node.type) {\n    case \"ClassProperty\":\n    case \"ClassPrivateProperty\":\n      return FIELD;\n    case \"ClassAccessorProperty\":\n      return ACCESSOR;\n    case \"ClassMethod\":\n    case \"ClassPrivateMethod\":\n      if (element.node.kind === \"get\") {\n        return GETTER;\n      } else if (element.node.kind === \"set\") {\n        return SETTER;\n      } else {\n        return METHOD;\n      }\n  }\n}\n\n// Information about the decorators applied to an element\ninterface DecoratorInfo {\n  // An array of applied decorators or a memoised identifier\n  decoratorsArray: t.Identifier | t.ArrayExpression | t.Expression;\n  decoratorsHaveThis: boolean;\n\n  // The kind of the decorated value, matches the kind value passed to applyDecs\n  kind: number;\n\n  // whether or not the field is static\n  isStatic: boolean;\n\n  // The name of the decorator\n  name: t.StringLiteral | t.Expression;\n\n  privateMethods:\n    | (t.FunctionExpression | t.ArrowFunctionExpression)[]\n    | undefined;\n\n  // The names of local variables that will be used/returned from the decoration\n  locals: t.Identifier | t.Identifier[] | undefined;\n}\n\n/**\n * Sort decoration info in the application order:\n * - static non-fields\n * - instance non-fields\n * - static fields\n * - instance fields\n *\n * @param {DecoratorInfo[]} info\n * @returns {DecoratorInfo[]} Sorted decoration info\n */\nfunction toSortedDecoratorInfo(info: DecoratorInfo[]): DecoratorInfo[] {\n  return [\n    ...info.filter(\n      el => el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...info.filter(\n      el => !el.isStatic && el.kind >= ACCESSOR && el.kind <= SETTER,\n    ),\n    ...info.filter(el => el.isStatic && el.kind === FIELD),\n    ...info.filter(el => !el.isStatic && el.kind === FIELD),\n  ];\n}\n\ntype GenerateDecorationListResult = {\n  // The zipped decorators array that will be passed to generateDecorationExprs\n  decs: t.Expression[];\n  // Whether there are non-empty decorator this values\n  haveThis: boolean;\n};\n/**\n * Zip decorators and decorator this values into an array\n *\n * @param {t.Expression[]} decorators\n * @param {((t.Expression | undefined)[])} decoratorsThis decorator this values\n * @param {DecoratorVersionKind} version\n * @returns {GenerateDecorationListResult}\n */\nfunction generateDecorationList(\n  decorators: t.Expression[],\n  decoratorsThis: (t.Expression | undefined)[],\n  version: DecoratorVersionKind,\n): GenerateDecorationListResult {\n  const decsCount = decorators.length;\n  const haveOneThis = decoratorsThis.some(Boolean);\n  const decs: t.Expression[] = [];\n  for (let i = 0; i < decsCount; i++) {\n    if (\n      (version === \"2023-11\" ||\n        (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n      haveOneThis\n    ) {\n      decs.push(\n        decoratorsThis[i] || t.unaryExpression(\"void\", t.numericLiteral(0)),\n      );\n    }\n    decs.push(decorators[i]);\n  }\n\n  return { haveThis: haveOneThis, decs };\n}\n\nfunction generateDecorationExprs(\n  decorationInfo: DecoratorInfo[],\n  version: DecoratorVersionKind,\n): t.ArrayExpression {\n  return t.arrayExpression(\n    decorationInfo.map(el => {\n      let flag = el.kind;\n      if (el.isStatic) {\n        flag +=\n          version === \"2023-11\" ||\n          (!process.env.BABEL_8_BREAKING && version === \"2023-05\")\n            ? STATIC\n            : STATIC_OLD_VERSION;\n      }\n      if (el.decoratorsHaveThis) flag += DECORATORS_HAVE_THIS;\n\n      return t.arrayExpression([\n        el.decoratorsArray,\n        t.numericLiteral(flag),\n        el.name,\n        ...(el.privateMethods || []),\n      ]);\n    }),\n  );\n}\n\nfunction extractElementLocalAssignments(decorationInfo: DecoratorInfo[]) {\n  const localIds: t.Identifier[] = [];\n\n  for (const el of decorationInfo) {\n    const { locals } = el;\n\n    if (Array.isArray(locals)) {\n      localIds.push(...locals);\n    } else if (locals !== undefined) {\n      localIds.push(locals);\n    }\n  }\n\n  return localIds;\n}\n\nfunction addCallAccessorsFor(\n  version: DecoratorVersionKind,\n  element: NodePath,\n  key: t.PrivateName,\n  getId: t.Identifier,\n  setId: t.Identifier,\n  isStatic: boolean,\n) {\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"get\",\n      t.cloneNode(key),\n      [],\n      t.blockStatement([\n        t.returnStatement(\n          t.callExpression(\n            t.cloneNode(getId),\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()],\n          ),\n        ),\n      ]),\n      isStatic,\n    ),\n  );\n\n  element.insertAfter(\n    t.classPrivateMethod(\n      \"set\",\n      t.cloneNode(key),\n      [t.identifier(\"v\")],\n      t.blockStatement([\n        t.expressionStatement(\n          t.callExpression(\n            t.cloneNode(setId),\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? [t.identifier(\"v\")]\n              : [t.thisExpression(), t.identifier(\"v\")],\n          ),\n        ),\n      ]),\n      isStatic,\n    ),\n  );\n}\n\nfunction movePrivateAccessor(\n  element: NodePath<t.ClassPrivateMethod>,\n  key: t.PrivateName,\n  methodLocalVar: t.Identifier,\n  isStatic: boolean,\n) {\n  let params: (t.Identifier | t.RestElement)[];\n  let block: t.Statement[];\n\n  if (element.node.kind === \"set\") {\n    params = [t.identifier(\"v\")];\n    block = [\n      t.expressionStatement(\n        t.callExpression(methodLocalVar, [\n          t.thisExpression(),\n          t.identifier(\"v\"),\n        ]),\n      ),\n    ];\n  } else {\n    params = [];\n    block = [\n      t.returnStatement(t.callExpression(methodLocalVar, [t.thisExpression()])),\n    ];\n  }\n\n  element.replaceWith(\n    t.classPrivateMethod(\n      element.node.kind,\n      t.cloneNode(key),\n      params,\n      t.blockStatement(block),\n      isStatic,\n    ),\n  );\n}\n\nfunction isClassDecoratableElementPath(\n  path: NodePath<ClassElement>,\n): path is NodePath<ClassDecoratableElement> {\n  const { type } = path;\n\n  return (\n    type !== \"TSDeclareMethod\" &&\n    type !== \"TSIndexSignature\" &&\n    type !== \"StaticBlock\"\n  );\n}\n\nfunction staticBlockToIIFE(block: t.StaticBlock) {\n  return t.callExpression(\n    t.arrowFunctionExpression([], t.blockStatement(block.body)),\n    [],\n  );\n}\n\nfunction staticBlockToFunctionClosure(block: t.StaticBlock) {\n  return t.functionExpression(null, [], t.blockStatement(block.body));\n}\n\nfunction fieldInitializerToClosure(value: t.Expression) {\n  return t.functionExpression(\n    null,\n    [],\n    t.blockStatement([t.returnStatement(value)]),\n  );\n}\n\nfunction maybeSequenceExpression(exprs: t.Expression[]) {\n  if (exprs.length === 0) return t.unaryExpression(\"void\", t.numericLiteral(0));\n  if (exprs.length === 1) return exprs[0];\n  return t.sequenceExpression(exprs);\n}\n\n/**\n * Create FunctionExpression from a ClassPrivateMethod.\n * The returned FunctionExpression node takes ownership of the private method's body and params.\n *\n * @param {t.ClassPrivateMethod} node\n * @returns\n */\nfunction createFunctionExpressionFromPrivateMethod(node: t.ClassPrivateMethod) {\n  const { params, body, generator: isGenerator, async: isAsync } = node;\n  return t.functionExpression(\n    undefined,\n    // @ts-expect-error todo: Improve typings: TSParameterProperty is only allowed in constructor\n    params,\n    body,\n    isGenerator,\n    isAsync,\n  );\n}\n\nfunction createSetFunctionNameCall(\n  state: PluginPass,\n  className: t.Identifier | t.StringLiteral,\n) {\n  return t.callExpression(state.addHelper(\"setFunctionName\"), [\n    t.thisExpression(),\n    className,\n  ]);\n}\n\nfunction createToPropertyKeyCall(state: PluginPass, propertyKey: t.Expression) {\n  return t.callExpression(state.addHelper(\"toPropertyKey\"), [propertyKey]);\n}\n\nfunction createPrivateBrandCheckClosure(brandName: t.PrivateName) {\n  return t.arrowFunctionExpression(\n    [t.identifier(\"_\")],\n    t.binaryExpression(\"in\", t.cloneNode(brandName), t.identifier(\"_\")),\n  );\n}\n\n// Check if the expression does not reference function-specific\n// context or the given identifier name.\n// `true` means \"maybe\" and `false` means \"no\".\nfunction usesFunctionContextOrYieldAwait(expression: t.Node) {\n  try {\n    t.traverseFast(expression, node => {\n      if (\n        t.isThisExpression(node) ||\n        t.isSuper(node) ||\n        t.isYieldExpression(node) ||\n        t.isAwaitExpression(node) ||\n        t.isIdentifier(node, { name: \"arguments\" }) ||\n        (t.isMetaProperty(node) && node.meta.name !== \"import\")\n      ) {\n        // TODO: Add early return support to t.traverseFast\n        throw null;\n      }\n    });\n    return false;\n  } catch {\n    return true;\n  }\n}\n\nfunction usesPrivateField(expression: t.Node) {\n  try {\n    t.traverseFast(expression, node => {\n      if (t.isPrivateName(node)) {\n        // TODO: Add early return support to t.traverseFast\n        throw null;\n      }\n    });\n    return false;\n  } catch {\n    return true;\n  }\n}\n\n/**\n * Convert a non-computed class element to its equivalent computed form.\n *\n * This function is to provide a decorator evaluation storage from non-computed\n * class elements.\n *\n * @param {(NodePath<t.ClassProperty | t.ClassMethod>)} path A non-computed class property or method\n */\nfunction convertToComputedKey(path: NodePath<t.ClassProperty | t.ClassMethod>) {\n  const { node } = path;\n  node.computed = true;\n  if (t.isIdentifier(node.key)) {\n    node.key = t.stringLiteral(node.key.name);\n  }\n}\n\nfunction hasInstancePrivateAccess(path: NodePath, privateNames: string[]) {\n  let containsInstancePrivateAccess = false;\n  if (privateNames.length > 0) {\n    const privateNameVisitor = privateNameVisitorFactory<\n      PrivateNameVisitorState<null>,\n      null\n    >({\n      PrivateName(path, state) {\n        if (state.privateNamesMap.has(path.node.id.name)) {\n          containsInstancePrivateAccess = true;\n          path.stop();\n        }\n      },\n    });\n    const privateNamesMap = new Map<string, null>();\n    for (const name of privateNames) {\n      privateNamesMap.set(name, null);\n    }\n    path.traverse(privateNameVisitor, {\n      privateNamesMap: privateNamesMap,\n    });\n  }\n  return containsInstancePrivateAccess;\n}\n\nfunction checkPrivateMethodUpdateError(\n  path: NodePath<t.Class>,\n  decoratedPrivateMethods: Set<string>,\n) {\n  const privateNameVisitor = privateNameVisitorFactory<\n    PrivateNameVisitorState<null>,\n    null\n  >({\n    PrivateName(path, state) {\n      if (!state.privateNamesMap.has(path.node.id.name)) return;\n\n      const parentPath = path.parentPath;\n      const parentParentPath = parentPath.parentPath;\n\n      if (\n        // this.bar().#x = 123;\n        (parentParentPath.node.type === \"AssignmentExpression\" &&\n          parentParentPath.node.left === parentPath.node) ||\n        // this.#x++;\n        parentParentPath.node.type === \"UpdateExpression\" ||\n        // ([...this.#x] = foo);\n        parentParentPath.node.type === \"RestElement\" ||\n        // ([this.#x] = foo);\n        parentParentPath.node.type === \"ArrayPattern\" ||\n        // ({ a: this.#x } = bar);\n        (parentParentPath.node.type === \"ObjectProperty\" &&\n          parentParentPath.node.value === parentPath.node &&\n          parentParentPath.parentPath.type === \"ObjectPattern\") ||\n        // for (this.#x of []);\n        (parentParentPath.node.type === \"ForOfStatement\" &&\n          parentParentPath.node.left === parentPath.node)\n      ) {\n        throw path.buildCodeFrameError(\n          `Decorated private methods are read-only, but \"#${path.node.id.name}\" is updated via this expression.`,\n        );\n      }\n    },\n  });\n  const privateNamesMap = new Map<string, null>();\n  for (const name of decoratedPrivateMethods) {\n    privateNamesMap.set(name, null);\n  }\n  path.traverse(privateNameVisitor, {\n    privateNamesMap: privateNamesMap,\n  });\n}\n\nfunction transformClass(\n  path: NodePath<t.Class>,\n  state: PluginPass,\n  constantSuper: boolean,\n  ignoreFunctionLength: boolean,\n  className: string | t.Identifier | t.StringLiteral | undefined,\n  propertyVisitor: Visitor<PluginPass>,\n  version: DecoratorVersionKind,\n): NodePath {\n  const body = path.get(\"body.body\");\n\n  const classDecorators = path.node.decorators;\n  let hasElementDecorators = false;\n  let hasComputedKeysSideEffects = false;\n  let elemDecsUseFnContext = false;\n\n  const generateClassPrivateUid = createLazyPrivateUidGeneratorForClass(path);\n\n  const classAssignments: t.AssignmentExpression[] = [];\n  const scopeParent: Scope = path.scope.parent;\n  const memoiseExpression = (\n    expression: t.Expression,\n    hint: string,\n    assignments: t.AssignmentExpression[],\n  ) => {\n    const localEvaluatedId = generateLetUidIdentifier(scopeParent, hint);\n    assignments.push(t.assignmentExpression(\"=\", localEvaluatedId, expression));\n    return t.cloneNode(localEvaluatedId);\n  };\n\n  let protoInitLocal: t.Identifier;\n  let staticInitLocal: t.Identifier;\n  const instancePrivateNames: string[] = [];\n  // Iterate over the class to see if we need to decorate it, and also to\n  // transform simple auto accessors which are not decorated, and handle inferred\n  // class name when the initializer of the class field is a class expression\n  for (const element of body) {\n    if (!isClassDecoratableElementPath(element)) {\n      continue;\n    }\n\n    const elementNode = element.node;\n\n    if (!elementNode.static && t.isPrivateName(elementNode.key)) {\n      instancePrivateNames.push(elementNode.key.id.name);\n    }\n\n    if (isDecorated(elementNode)) {\n      switch (elementNode.type) {\n        case \"ClassProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassProperty should be callable. Improve typings.\n          propertyVisitor.ClassProperty(\n            element as NodePath<t.ClassProperty>,\n            state,\n          );\n          break;\n        case \"ClassPrivateProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassPrivateProperty should be callable. Improve typings.\n          propertyVisitor.ClassPrivateProperty(\n            element as NodePath<t.ClassPrivateProperty>,\n            state,\n          );\n          break;\n        case \"ClassAccessorProperty\":\n          // @ts-expect-error todo: propertyVisitor.ClassAccessorProperty should be callable. Improve typings.\n          propertyVisitor.ClassAccessorProperty(\n            element as NodePath<t.ClassAccessorProperty>,\n            state,\n          );\n          if (version === \"2023-11\") {\n            break;\n          }\n        /* fallthrough */\n        default:\n          if (elementNode.static) {\n            staticInitLocal ??= generateLetUidIdentifier(\n              scopeParent,\n              \"initStatic\",\n            );\n          } else {\n            protoInitLocal ??= generateLetUidIdentifier(\n              scopeParent,\n              \"initProto\",\n            );\n          }\n          break;\n      }\n      hasElementDecorators = true;\n      elemDecsUseFnContext ||= elementNode.decorators.some(\n        usesFunctionContextOrYieldAwait,\n      );\n    } else if (elementNode.type === \"ClassAccessorProperty\") {\n      // @ts-expect-error todo: propertyVisitor.ClassAccessorProperty should be callable. Improve typings.\n      propertyVisitor.ClassAccessorProperty(\n        element as NodePath<t.ClassAccessorProperty>,\n        state,\n      );\n      const { key, value, static: isStatic, computed } = elementNode;\n\n      const newId = generateClassPrivateUid();\n      const newField = generateClassProperty(newId, value, isStatic);\n      const keyPath = element.get(\"key\");\n      const [newPath] = element.replaceWith(newField);\n\n      let getterKey, setterKey;\n      if (computed && !keyPath.isConstantExpression()) {\n        getterKey = memoiseComputedKey(\n          createToPropertyKeyCall(state, key as t.Expression),\n          scopeParent,\n          scopeParent.generateUid(\"computedKey\"),\n        )!;\n        setterKey = t.cloneNode(getterKey.left as t.Identifier);\n      } else {\n        getterKey = t.cloneNode(key);\n        setterKey = t.cloneNode(key);\n      }\n\n      addProxyAccessorsFor(\n        path.node.id,\n        newPath,\n        getterKey,\n        setterKey,\n        newId,\n        computed,\n        isStatic,\n        version,\n      );\n    }\n\n    if (\"computed\" in element.node && element.node.computed) {\n      hasComputedKeysSideEffects ||= !scopeParent.isStatic(element.node.key);\n    }\n  }\n\n  if (!classDecorators && !hasElementDecorators) {\n    // If nothing is decorated and no assignments inserted, return\n    return;\n  }\n\n  const elementDecoratorInfo: DecoratorInfo[] = [];\n\n  let constructorPath: NodePath<t.ClassMethod> | undefined;\n  const decoratedPrivateMethods = new Set<string>();\n\n  let classInitLocal: t.Identifier, classIdLocal: t.Identifier;\n  let decoratorReceiverId: t.Identifier | null = null;\n\n  // Memoise the this value `a.b` of decorator member expressions `@a.b.dec`,\n  type HandleDecoratorExpressionsResult = {\n    // whether the whole decorator list requires memoisation\n    hasSideEffects: boolean;\n    usesFnContext: boolean;\n    // the this value of each decorator if applicable\n    decoratorsThis: (t.Expression | undefined)[];\n  };\n  function handleDecoratorExpressions(\n    expressions: t.Expression[],\n  ): HandleDecoratorExpressionsResult {\n    let hasSideEffects = false;\n    let usesFnContext = false;\n    const decoratorsThis: (t.Expression | null)[] = [];\n    for (const expression of expressions) {\n      let object;\n      if (\n        (version === \"2023-11\" ||\n          (!process.env.BABEL_8_BREAKING && version === \"2023-05\")) &&\n        t.isMemberExpression(expression)\n      ) {\n        if (t.isSuper(expression.object)) {\n          object = t.thisExpression();\n        } else if (scopeParent.isStatic(expression.object)) {\n          object = t.cloneNode(expression.object);\n        } else {\n          decoratorReceiverId ??= generateLetUidIdentifier(scopeParent, \"obj\");\n          object = t.assignmentExpression(\n            \"=\",\n            t.cloneNode(decoratorReceiverId),\n            expression.object,\n          );\n          expression.object = t.cloneNode(decoratorReceiverId);\n        }\n      }\n      decoratorsThis.push(object);\n      hasSideEffects ||= !scopeParent.isStatic(expression);\n      usesFnContext ||= usesFunctionContextOrYieldAwait(expression);\n    }\n    return { hasSideEffects, usesFnContext, decoratorsThis };\n  }\n\n  const willExtractSomeElemDecs =\n    hasComputedKeysSideEffects ||\n    (process.env.BABEL_8_BREAKING\n      ? elemDecsUseFnContext\n      : elemDecsUseFnContext || version !== \"2023-11\");\n\n  let needsDeclaraionForClassBinding = false;\n  let classDecorationsFlag = 0;\n  let classDecorations: t.Expression[] = [];\n  let classDecorationsId: t.Identifier;\n  let computedKeyAssignments: t.AssignmentExpression[] = [];\n  if (classDecorators) {\n    classInitLocal = generateLetUidIdentifier(scopeParent, \"initClass\");\n    needsDeclaraionForClassBinding = path.isClassDeclaration();\n    ({ id: classIdLocal, path } = replaceClassWithVar(path, className));\n\n    path.node.decorators = null;\n\n    const decoratorExpressions = classDecorators.map(el => el.expression);\n    const classDecsUsePrivateName = decoratorExpressions.some(usesPrivateField);\n    const { hasSideEffects, decoratorsThis } =\n      handleDecoratorExpressions(decoratorExpressions);\n\n    const { haveThis, decs } = generateDecorationList(\n      decoratorExpressions,\n      decoratorsThis,\n      version,\n    );\n    classDecorationsFlag = haveThis ? 1 : 0;\n    classDecorations = decs;\n\n    if (\n      (hasSideEffects && willExtractSomeElemDecs) ||\n      classDecsUsePrivateName\n    ) {\n      classDecorationsId = memoiseExpression(\n        t.arrayExpression(classDecorations),\n        \"classDecs\",\n        classAssignments,\n      );\n    }\n\n    if (!hasElementDecorators) {\n      // Sync body paths as non-decorated computed accessors have been transpiled\n      // to getter-setter pairs.\n      for (const element of path.get(\"body.body\")) {\n        const { node } = element;\n        const isComputed = \"computed\" in node && node.computed;\n        if (isComputed) {\n          if (element.isClassProperty({ static: true })) {\n            if (!element.get(\"key\").isConstantExpression()) {\n              const key = (node as t.ClassProperty).key;\n              const maybeAssignment = memoiseComputedKey(\n                key,\n                scopeParent,\n                scopeParent.generateUid(\"computedKey\"),\n              );\n              if (maybeAssignment != null) {\n                // If it is a static computed field within a decorated class, we move the computed key\n                // into `computedKeyAssignments` which will be then moved into the non-static class,\n                // to ensure that the evaluation order and private environment are correct\n                node.key = t.cloneNode(maybeAssignment.left);\n                computedKeyAssignments.push(maybeAssignment);\n              }\n            }\n          } else if (computedKeyAssignments.length > 0) {\n            prependExpressionsToComputedKey(\n              computedKeyAssignments,\n              element as NodePath<ClassElementCanHaveComputedKeys>,\n            );\n            computedKeyAssignments = [];\n          }\n        }\n      }\n    }\n  } else {\n    if (!path.node.id) {\n      path.node.id = path.scope.generateUidIdentifier(\"Class\");\n    }\n    classIdLocal = t.cloneNode(path.node.id);\n  }\n\n  let lastInstancePrivateName: t.PrivateName;\n  let needsInstancePrivateBrandCheck = false;\n\n  let fieldInitializerExpressions = [];\n  let staticFieldInitializerExpressions: t.Expression[] = [];\n\n  if (hasElementDecorators) {\n    if (protoInitLocal) {\n      const protoInitCall = t.callExpression(t.cloneNode(protoInitLocal), [\n        t.thisExpression(),\n      ]);\n      fieldInitializerExpressions.push(protoInitCall);\n    }\n    for (const element of body) {\n      if (!isClassDecoratableElementPath(element)) {\n        if (\n          staticFieldInitializerExpressions.length > 0 &&\n          element.isStaticBlock()\n        ) {\n          prependExpressionsToStaticBlock(\n            staticFieldInitializerExpressions,\n            element,\n          );\n          staticFieldInitializerExpressions = [];\n        }\n        continue;\n      }\n\n      const { node } = element;\n      const decorators = node.decorators;\n\n      const hasDecorators = !!decorators?.length;\n\n      const isComputed = \"computed\" in node && node.computed;\n\n      let name = \"computedKey\";\n\n      if (node.key.type === \"PrivateName\") {\n        name = node.key.id.name;\n      } else if (!isComputed && node.key.type === \"Identifier\") {\n        name = node.key.name;\n      }\n      let decoratorsArray: t.Identifier | t.ArrayExpression | t.Expression;\n      let decoratorsHaveThis;\n\n      if (hasDecorators) {\n        const decoratorExpressions = decorators.map(d => d.expression);\n        const { hasSideEffects, usesFnContext, decoratorsThis } =\n          handleDecoratorExpressions(decoratorExpressions);\n        const { decs, haveThis } = generateDecorationList(\n          decoratorExpressions,\n          decoratorsThis,\n          version,\n        );\n        decoratorsHaveThis = haveThis;\n        decoratorsArray = decs.length === 1 ? decs[0] : t.arrayExpression(decs);\n        if (usesFnContext || (hasSideEffects && willExtractSomeElemDecs)) {\n          decoratorsArray = memoiseExpression(\n            decoratorsArray,\n            name + \"Decs\",\n            computedKeyAssignments,\n          );\n        }\n      }\n\n      if (isComputed) {\n        if (!element.get(\"key\").isConstantExpression()) {\n          const key = node.key as t.Expression;\n          const maybeAssignment = memoiseComputedKey(\n            hasDecorators ? createToPropertyKeyCall(state, key) : key,\n            scopeParent,\n            scopeParent.generateUid(\"computedKey\"),\n          );\n          if (maybeAssignment != null) {\n            // If it is a static computed field within a decorated class, we move the computed key\n            // into `computedKeyAssignments` which will be then moved into the non-static class,\n            // to ensure that the evaluation order and private environment are correct\n            if (classDecorators && element.isClassProperty({ static: true })) {\n              node.key = t.cloneNode(maybeAssignment.left);\n              computedKeyAssignments.push(maybeAssignment);\n            } else {\n              node.key = maybeAssignment;\n            }\n          }\n        }\n      }\n\n      const { key, static: isStatic } = node;\n\n      const isPrivate = key.type === \"PrivateName\";\n\n      const kind = getElementKind(element);\n\n      if (isPrivate && !isStatic) {\n        if (hasDecorators) {\n          needsInstancePrivateBrandCheck = true;\n        }\n        if (t.isClassPrivateProperty(node) || !lastInstancePrivateName) {\n          lastInstancePrivateName = key;\n        }\n      }\n\n      if (element.isClassMethod({ kind: \"constructor\" })) {\n        constructorPath = element;\n      }\n\n      let locals: t.Identifier[];\n      if (hasDecorators) {\n        let privateMethods: Array<\n          t.FunctionExpression | t.ArrowFunctionExpression\n        >;\n\n        let nameExpr: t.Expression;\n\n        if (isComputed) {\n          nameExpr = getComputedKeyMemoiser(\n            element.get(\"key\") as NodePath<t.Expression>,\n          );\n        } else if (key.type === \"PrivateName\") {\n          nameExpr = t.stringLiteral(key.id.name);\n        } else if (key.type === \"Identifier\") {\n          nameExpr = t.stringLiteral(key.name);\n        } else {\n          nameExpr = t.cloneNode(key as t.Expression);\n        }\n\n        if (kind === ACCESSOR) {\n          const { value } = element.node as t.ClassAccessorProperty;\n\n          const params: t.Expression[] =\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()];\n\n          if (value) {\n            params.push(t.cloneNode(value));\n          }\n\n          const newId = generateClassPrivateUid();\n          const newFieldInitId = generateLetUidIdentifier(\n            scopeParent,\n            `init_${name}`,\n          );\n          const newValue = t.callExpression(\n            t.cloneNode(newFieldInitId),\n            params,\n          );\n\n          const newField = generateClassProperty(newId, newValue, isStatic);\n          const [newPath] = element.replaceWith(newField);\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(newId, version);\n\n            const getId = generateLetUidIdentifier(scopeParent, `get_${name}`);\n            const setId = generateLetUidIdentifier(scopeParent, `set_${name}`);\n\n            addCallAccessorsFor(version, newPath, key, getId, setId, isStatic);\n\n            locals = [newFieldInitId, getId, setId];\n          } else {\n            addProxyAccessorsFor(\n              path.node.id,\n              newPath,\n              t.cloneNode(key),\n              t.isAssignmentExpression(key)\n                ? t.cloneNode(key.left as t.Identifier)\n                : t.cloneNode(key),\n              newId,\n              isComputed,\n              isStatic,\n              version,\n            );\n            locals = [newFieldInitId];\n          }\n        } else if (kind === FIELD) {\n          const initId = generateLetUidIdentifier(scopeParent, `init_${name}`);\n          const valuePath = (\n            element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n          ).get(\"value\");\n\n          const args: t.Expression[] =\n            (process.env.BABEL_8_BREAKING || version === \"2023-11\") && isStatic\n              ? []\n              : [t.thisExpression()];\n          if (valuePath.node) args.push(valuePath.node);\n\n          valuePath.replaceWith(t.callExpression(t.cloneNode(initId), args));\n\n          locals = [initId];\n\n          if (isPrivate) {\n            privateMethods = extractProxyAccessorsFor(key, version);\n          }\n        } else if (isPrivate) {\n          const callId = generateLetUidIdentifier(scopeParent, `call_${name}`);\n          locals = [callId];\n\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element as NodePath<t.ClassPrivateMethod>,\n            objectRef: classIdLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classIdLocal,\n          });\n\n          replaceSupers.replace();\n\n          privateMethods = [\n            createFunctionExpressionFromPrivateMethod(\n              element.node as t.ClassPrivateMethod,\n            ),\n          ];\n\n          if (kind === GETTER || kind === SETTER) {\n            movePrivateAccessor(\n              element as NodePath<t.ClassPrivateMethod>,\n              t.cloneNode(key),\n              t.cloneNode(callId),\n              isStatic,\n            );\n          } else {\n            const node = element.node as t.ClassPrivateMethod;\n\n            // Unshift\n            path.node.body.body.unshift(\n              t.classPrivateProperty(key, t.cloneNode(callId), [], node.static),\n            );\n\n            decoratedPrivateMethods.add(key.id.name);\n\n            element.remove();\n          }\n        }\n\n        elementDecoratorInfo.push({\n          kind,\n          decoratorsArray,\n          decoratorsHaveThis,\n          name: nameExpr,\n          isStatic,\n          privateMethods,\n          locals,\n        });\n\n        if (element.node) {\n          element.node.decorators = null;\n        }\n      }\n\n      if (isComputed && computedKeyAssignments.length > 0) {\n        if (classDecorators && element.isClassProperty({ static: true })) {\n          // If the class is decorated, we don't insert computedKeyAssignments here\n          // because any non-static computed elements defined after it will be moved\n          // into the non-static class, so they will be evaluated before the key of\n          // this field. At this momemnt, its key must be either a constant expression\n          // or a uid reference which has been assigned _within_ the non-static class.\n        } else {\n          prependExpressionsToComputedKey(\n            computedKeyAssignments,\n            (kind === ACCESSOR\n              ? element.getNextSibling() // the transpiled getter of the accessor property\n              : element) as NodePath<ClassElementCanHaveComputedKeys>,\n          );\n          computedKeyAssignments = [];\n        }\n      }\n\n      if (\n        fieldInitializerExpressions.length > 0 &&\n        !isStatic &&\n        (kind === FIELD || kind === ACCESSOR)\n      ) {\n        prependExpressionsToFieldInitializer(\n          fieldInitializerExpressions,\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n        );\n        fieldInitializerExpressions = [];\n      }\n\n      if (\n        staticFieldInitializerExpressions.length > 0 &&\n        isStatic &&\n        (kind === FIELD || kind === ACCESSOR)\n      ) {\n        prependExpressionsToFieldInitializer(\n          staticFieldInitializerExpressions,\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>,\n        );\n        staticFieldInitializerExpressions = [];\n      }\n\n      if (hasDecorators && version === \"2023-11\") {\n        if (kind === FIELD || kind === ACCESSOR) {\n          const initExtraId = generateLetUidIdentifier(\n            scopeParent,\n            `init_extra_${name}`,\n          );\n          locals.push(initExtraId);\n          const initExtraCall = t.callExpression(\n            t.cloneNode(initExtraId),\n            isStatic ? [] : [t.thisExpression()],\n          );\n          if (!isStatic) {\n            fieldInitializerExpressions.push(initExtraCall);\n          } else {\n            staticFieldInitializerExpressions.push(initExtraCall);\n          }\n        }\n      }\n    }\n  }\n\n  if (computedKeyAssignments.length > 0) {\n    const elements = path.get(\"body.body\");\n    let lastComputedElement: NodePath<ClassElementCanHaveComputedKeys>;\n    for (let i = elements.length - 1; i >= 0; i--) {\n      const path = elements[i];\n      const node = path.node as ClassElementCanHaveComputedKeys;\n      if (node.computed) {\n        if (classDecorators && t.isClassProperty(node, { static: true })) {\n          continue;\n        }\n        lastComputedElement = path as NodePath<ClassElementCanHaveComputedKeys>;\n        break;\n      }\n    }\n    if (lastComputedElement != null) {\n      appendExpressionsToComputedKey(\n        computedKeyAssignments,\n        lastComputedElement,\n      );\n      computedKeyAssignments = [];\n    } else {\n      // If there is no computed key, we will try to convert the first non-computed\n      // class element into a computed key and insert assignments there. This will\n      // be done after we handle the class elements split when the class is decorated.\n    }\n  }\n\n  if (fieldInitializerExpressions.length > 0) {\n    const isDerivedClass = !!path.node.superClass;\n    if (constructorPath) {\n      if (isDerivedClass) {\n        insertExpressionsAfterSuperCallAndOptimize(\n          fieldInitializerExpressions,\n          constructorPath,\n          protoInitLocal,\n        );\n      } else {\n        prependExpressionsToConstructor(\n          fieldInitializerExpressions,\n          constructorPath,\n        );\n      }\n    } else {\n      path.node.body.body.unshift(\n        createConstructorFromExpressions(\n          fieldInitializerExpressions,\n          isDerivedClass,\n        ),\n      );\n    }\n    fieldInitializerExpressions = [];\n  }\n\n  if (staticFieldInitializerExpressions.length > 0) {\n    path.node.body.body.push(\n      createStaticBlockFromExpressions(staticFieldInitializerExpressions),\n    );\n    staticFieldInitializerExpressions = [];\n  }\n\n  const sortedElementDecoratorInfo =\n    toSortedDecoratorInfo(elementDecoratorInfo);\n\n  const elementDecorations = generateDecorationExprs(\n    process.env.BABEL_8_BREAKING || version === \"2023-11\"\n      ? elementDecoratorInfo\n      : sortedElementDecoratorInfo,\n    version,\n  );\n\n  const elementLocals: t.Identifier[] = extractElementLocalAssignments(\n    sortedElementDecoratorInfo,\n  );\n\n  if (protoInitLocal) {\n    elementLocals.push(protoInitLocal);\n  }\n\n  if (staticInitLocal) {\n    elementLocals.push(staticInitLocal);\n  }\n\n  const classLocals: t.Identifier[] = [];\n  let classInitInjected = false;\n  const classInitCall =\n    classInitLocal && t.callExpression(t.cloneNode(classInitLocal), []);\n\n  let originalClassPath = path;\n  const originalClass = path.node;\n\n  const staticClosures: t.AssignmentExpression[] = [];\n  if (classDecorators) {\n    classLocals.push(classIdLocal, classInitLocal);\n    const statics: (\n      | t.ClassProperty\n      | t.ClassPrivateProperty\n      | t.ClassPrivateMethod\n    )[] = [];\n    path.get(\"body.body\").forEach(element => {\n      // Static blocks cannot be compiled to \"instance blocks\", but we can inline\n      // them as IIFEs in the next property.\n      if (element.isStaticBlock()) {\n        if (hasInstancePrivateAccess(element, instancePrivateNames)) {\n          const staticBlockClosureId = memoiseExpression(\n            staticBlockToFunctionClosure(element.node),\n            \"staticBlock\",\n            staticClosures,\n          );\n          staticFieldInitializerExpressions.push(\n            t.callExpression(\n              t.memberExpression(staticBlockClosureId, t.identifier(\"call\")),\n              [t.thisExpression()],\n            ),\n          );\n        } else {\n          staticFieldInitializerExpressions.push(\n            staticBlockToIIFE(element.node),\n          );\n        }\n        element.remove();\n        return;\n      }\n\n      if (\n        (element.isClassProperty() || element.isClassPrivateProperty()) &&\n        element.node.static\n      ) {\n        const valuePath = (\n          element as NodePath<t.ClassProperty | t.ClassPrivateProperty>\n        ).get(\"value\");\n        if (hasInstancePrivateAccess(valuePath, instancePrivateNames)) {\n          const fieldValueClosureId = memoiseExpression(\n            fieldInitializerToClosure(valuePath.node),\n            \"fieldValue\",\n            staticClosures,\n          );\n          valuePath.replaceWith(\n            t.callExpression(\n              t.memberExpression(fieldValueClosureId, t.identifier(\"call\")),\n              [t.thisExpression()],\n            ),\n          );\n        }\n        if (staticFieldInitializerExpressions.length > 0) {\n          prependExpressionsToFieldInitializer(\n            staticFieldInitializerExpressions,\n            element,\n          );\n          staticFieldInitializerExpressions = [];\n        }\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      } else if (element.isClassPrivateMethod({ static: true })) {\n        // At this moment the element must not have decorators, so any private name\n        // within the element must come from either params or body\n        if (hasInstancePrivateAccess(element, instancePrivateNames)) {\n          const replaceSupers = new ReplaceSupers({\n            constantSuper,\n            methodPath: element,\n            objectRef: classIdLocal,\n            superRef: path.node.superClass,\n            file: state.file,\n            refToPreserve: classIdLocal,\n          });\n\n          replaceSupers.replace();\n\n          const privateMethodDelegateId = memoiseExpression(\n            createFunctionExpressionFromPrivateMethod(element.node),\n            element.get(\"key.id\").node.name,\n            staticClosures,\n          );\n\n          if (ignoreFunctionLength) {\n            element.node.params = [t.restElement(t.identifier(\"arg\"))];\n            element.node.body = t.blockStatement([\n              t.returnStatement(\n                t.callExpression(\n                  t.memberExpression(\n                    privateMethodDelegateId,\n                    t.identifier(\"apply\"),\n                  ),\n                  [t.thisExpression(), t.identifier(\"arg\")],\n                ),\n              ),\n            ]);\n          } else {\n            element.node.params = element.node.params.map((p, i) => {\n              if (t.isRestElement(p)) {\n                return t.restElement(t.identifier(\"arg\"));\n              } else {\n                return t.identifier(\"_\" + i);\n              }\n            });\n            element.node.body = t.blockStatement([\n              t.returnStatement(\n                t.callExpression(\n                  t.memberExpression(\n                    privateMethodDelegateId,\n                    t.identifier(\"apply\"),\n                  ),\n                  [t.thisExpression(), t.identifier(\"arguments\")],\n                ),\n              ),\n            ]);\n          }\n        }\n        element.node.static = false;\n        statics.push(element.node);\n        element.remove();\n      }\n    });\n\n    if (statics.length > 0 || staticFieldInitializerExpressions.length > 0) {\n      const staticsClass = template.expression.ast`\n        class extends ${state.addHelper(\"identity\")} {}\n      ` as t.ClassExpression;\n      staticsClass.body.body = [\n        // Insert the original class to a computed key of the wrapper so that\n        // 1) they share the same function context with the wrapper class\n        // 2) the memoisation of static computed field is evaluated before they\n        //    are referenced in the wrapper class keys\n        // Note that any static elements of the wrapper class can not be accessed\n        // in the user land, so we don't have to remove the temporary class field.\n        t.classProperty(\n          t.toExpression(originalClass),\n          undefined,\n          undefined,\n          undefined,\n          /* computed */ true,\n          /* static */ true,\n        ),\n        ...statics,\n      ];\n\n      const constructorBody: t.Expression[] = [];\n\n      const newExpr = t.newExpression(staticsClass, []);\n\n      if (staticFieldInitializerExpressions.length > 0) {\n        constructorBody.push(...staticFieldInitializerExpressions);\n      }\n      if (classInitCall) {\n        classInitInjected = true;\n        constructorBody.push(classInitCall);\n      }\n      if (constructorBody.length > 0) {\n        constructorBody.unshift(\n          t.callExpression(t.super(), [t.cloneNode(classIdLocal)]),\n        );\n\n        // set isDerivedClass to false as we have already prepended super call\n        staticsClass.body.body.push(\n          createConstructorFromExpressions(\n            constructorBody,\n            /* isDerivedClass */ false,\n          ),\n        );\n      } else {\n        newExpr.arguments.push(t.cloneNode(classIdLocal));\n      }\n\n      const [newPath] = path.replaceWith(newExpr);\n\n      // update originalClassPath according to the new AST\n      originalClassPath = (\n        newPath.get(\"callee\").get(\"body\") as NodePath<t.ClassBody>\n      )\n        .get(\"body\")[0]\n        .get(\"key\");\n    }\n  }\n  if (!classInitInjected && classInitCall) {\n    path.node.body.body.push(\n      t.staticBlock([t.expressionStatement(classInitCall)]),\n    );\n  }\n\n  let { superClass } = originalClass;\n  if (\n    superClass &&\n    (process.env.BABEL_8_BREAKING ||\n      version === \"2023-11\" ||\n      version === \"2023-05\")\n  ) {\n    const id = path.scope.maybeGenerateMemoised(superClass);\n    if (id) {\n      originalClass.superClass = t.assignmentExpression(\"=\", id, superClass);\n      superClass = id;\n    }\n  }\n\n  const applyDecoratorWrapper = t.staticBlock([]);\n  originalClass.body.body.unshift(applyDecoratorWrapper);\n  const applyDecsBody = applyDecoratorWrapper.body;\n  if (computedKeyAssignments.length > 0) {\n    const elements = originalClassPath.get(\"body.body\");\n    let firstPublicElement: NodePath<t.ClassProperty | t.ClassMethod>;\n    for (const path of elements) {\n      if (\n        (path.isClassProperty() || path.isClassMethod()) &&\n        (path.node as t.ClassMethod).kind !== \"constructor\"\n      ) {\n        firstPublicElement = path;\n        break;\n      }\n    }\n    if (firstPublicElement != null) {\n      // Convert its key to a computed one to host the decorator evaluations.\n      convertToComputedKey(firstPublicElement);\n      prependExpressionsToComputedKey(\n        computedKeyAssignments,\n        firstPublicElement,\n      );\n    } else {\n      // When there is no public class elements, we inject a temporary computed\n      // field whose key will host the decorator evaluations. The field will be\n      // deleted immediately after it is defiend.\n      originalClass.body.body.unshift(\n        t.classProperty(\n          t.sequenceExpression([\n            ...computedKeyAssignments,\n            t.stringLiteral(\"_\"),\n          ]),\n          undefined,\n          undefined,\n          undefined,\n          /* computed */ true,\n          /* static */ true,\n        ),\n      );\n      applyDecsBody.push(\n        t.expressionStatement(\n          t.unaryExpression(\n            \"delete\",\n            t.memberExpression(t.thisExpression(), t.identifier(\"_\")),\n          ),\n        ),\n      );\n    }\n    computedKeyAssignments = [];\n  }\n\n  applyDecsBody.push(\n    t.expressionStatement(\n      createLocalsAssignment(\n        elementLocals,\n        classLocals,\n        elementDecorations,\n        classDecorationsId ?? t.arrayExpression(classDecorations),\n        t.numericLiteral(classDecorationsFlag),\n        needsInstancePrivateBrandCheck ? lastInstancePrivateName : null,\n        typeof className === \"object\" ? className : undefined,\n        t.cloneNode(superClass),\n        state,\n        version,\n      ),\n    ),\n  );\n  if (staticInitLocal) {\n    applyDecsBody.push(\n      t.expressionStatement(\n        t.callExpression(t.cloneNode(staticInitLocal), [t.thisExpression()]),\n      ),\n    );\n  }\n  if (staticClosures.length > 0) {\n    applyDecsBody.push(\n      ...staticClosures.map(expr => t.expressionStatement(expr)),\n    );\n  }\n\n  // When path is a ClassExpression, path.insertBefore will convert `path`\n  // into a SequenceExpression\n  path.insertBefore(classAssignments.map(expr => t.expressionStatement(expr)));\n\n  if (needsDeclaraionForClassBinding) {\n    path.insertBefore(\n      t.variableDeclaration(\"let\", [\n        t.variableDeclarator(t.cloneNode(classIdLocal)),\n      ]),\n    );\n  }\n\n  if (decoratedPrivateMethods.size > 0) {\n    checkPrivateMethodUpdateError(path, decoratedPrivateMethods);\n  }\n\n  // Recrawl the scope to make sure new identifiers are properly synced\n  path.scope.crawl();\n\n  return path;\n}\n\nfunction createLocalsAssignment(\n  elementLocals: t.Identifier[],\n  classLocals: t.Identifier[],\n  elementDecorations: t.ArrayExpression | t.Identifier,\n  classDecorations: t.ArrayExpression | t.Identifier,\n  classDecorationsFlag: t.NumericLiteral,\n  maybePrivateBrandName: t.PrivateName | null,\n  setClassName: t.Identifier | t.StringLiteral | undefined,\n  superClass: null | t.Expression,\n  state: PluginPass,\n  version: DecoratorVersionKind,\n) {\n  let lhs, rhs;\n  const args: t.Expression[] = [\n    setClassName\n      ? createSetFunctionNameCall(state, setClassName)\n      : t.thisExpression(),\n    classDecorations,\n    elementDecorations,\n  ];\n\n  if (!process.env.BABEL_8_BREAKING) {\n    if (version !== \"2023-11\") {\n      args.splice(1, 2, elementDecorations, classDecorations);\n    }\n    if (\n      version === \"2021-12\" ||\n      (version === \"2022-03\" && !state.availableHelper(\"applyDecs2203R\"))\n    ) {\n      lhs = t.arrayPattern([...elementLocals, ...classLocals]);\n      rhs = t.callExpression(\n        state.addHelper(version === \"2021-12\" ? \"applyDecs\" : \"applyDecs2203\"),\n        args,\n      );\n      return t.assignmentExpression(\"=\", lhs, rhs);\n    } else if (version === \"2022-03\") {\n      rhs = t.callExpression(state.addHelper(\"applyDecs2203R\"), args);\n    } else if (version === \"2023-01\") {\n      if (maybePrivateBrandName) {\n        args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n      }\n      rhs = t.callExpression(state.addHelper(\"applyDecs2301\"), args);\n    } else if (version === \"2023-05\") {\n      if (\n        maybePrivateBrandName ||\n        superClass ||\n        classDecorationsFlag.value !== 0\n      ) {\n        args.push(classDecorationsFlag);\n      }\n      if (maybePrivateBrandName) {\n        args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n      } else if (superClass) {\n        args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n      }\n      if (superClass) args.push(superClass);\n      rhs = t.callExpression(state.addHelper(\"applyDecs2305\"), args);\n    }\n  }\n  if (process.env.BABEL_8_BREAKING || version === \"2023-11\") {\n    if (\n      maybePrivateBrandName ||\n      superClass ||\n      classDecorationsFlag.value !== 0\n    ) {\n      args.push(classDecorationsFlag);\n    }\n    if (maybePrivateBrandName) {\n      args.push(createPrivateBrandCheckClosure(maybePrivateBrandName));\n    } else if (superClass) {\n      args.push(t.unaryExpression(\"void\", t.numericLiteral(0)));\n    }\n    if (superClass) args.push(superClass);\n    rhs = t.callExpression(state.addHelper(\"applyDecs2311\"), args);\n  }\n\n  // optimize `{ c: [classLocals] } = applyDecsHelper(...)` to\n  // `[classLocals] = applyDecsHelper(...).c`\n  if (elementLocals.length > 0) {\n    if (classLocals.length > 0) {\n      lhs = t.objectPattern([\n        t.objectProperty(t.identifier(\"e\"), t.arrayPattern(elementLocals)),\n        t.objectProperty(t.identifier(\"c\"), t.arrayPattern(classLocals)),\n      ]);\n    } else {\n      lhs = t.arrayPattern(elementLocals);\n      rhs = t.memberExpression(rhs, t.identifier(\"e\"), false, false);\n    }\n  } else {\n    // invariant: classLocals.length > 0\n    lhs = t.arrayPattern(classLocals);\n    rhs = t.memberExpression(rhs, t.identifier(\"c\"), false, false);\n  }\n\n  return t.assignmentExpression(\"=\", lhs, rhs);\n}\n\nfunction isProtoKey(\n  node: t.Identifier | t.StringLiteral | t.BigIntLiteral | t.NumericLiteral,\n) {\n  return node.type === \"Identifier\"\n    ? node.name === \"__proto__\"\n    : node.value === \"__proto__\";\n}\n\nfunction isDecorated(node: t.Class | ClassDecoratableElement) {\n  return node.decorators && node.decorators.length > 0;\n}\n\nfunction shouldTransformElement(node: ClassElement) {\n  switch (node.type) {\n    case \"ClassAccessorProperty\":\n      return true;\n    case \"ClassMethod\":\n    case \"ClassProperty\":\n    case \"ClassPrivateMethod\":\n    case \"ClassPrivateProperty\":\n      return isDecorated(node);\n    default:\n      return false;\n  }\n}\n\nfunction shouldTransformClass(node: t.Class) {\n  return isDecorated(node) || node.body.body.some(shouldTransformElement);\n}\n\n// Todo: unify name references logic with helper-function-name\nfunction NamedEvaluationVisitoryFactory(\n  isAnonymous: (path: NodePath) => boolean,\n  visitor: (\n    path: NodePath,\n    state: PluginPass,\n    name:\n      | string\n      | t.Identifier\n      | t.StringLiteral\n      | t.NumericLiteral\n      | t.BigIntLiteral,\n  ) => void,\n) {\n  function handleComputedProperty(\n    propertyPath: NodePath<\n      t.ObjectProperty | t.ClassProperty | t.ClassAccessorProperty\n    >,\n    key: t.Expression,\n    state: PluginPass,\n  ): t.StringLiteral | t.Identifier {\n    switch (key.type) {\n      case \"StringLiteral\":\n        return t.stringLiteral(key.value);\n      case \"NumericLiteral\":\n      case \"BigIntLiteral\": {\n        const keyValue = key.value + \"\";\n        propertyPath.get(\"key\").replaceWith(t.stringLiteral(keyValue));\n        return t.stringLiteral(keyValue);\n      }\n      default: {\n        const ref = propertyPath.scope.maybeGenerateMemoised(key);\n        propertyPath\n          .get(\"key\")\n          .replaceWith(\n            t.assignmentExpression(\n              \"=\",\n              ref,\n              createToPropertyKeyCall(state, key),\n            ),\n          );\n        return t.cloneNode(ref);\n      }\n    }\n  }\n  return {\n    VariableDeclarator(path, state) {\n      const id = path.node.id;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"init\"));\n        if (isAnonymous(initializer)) {\n          const name = id.name;\n          visitor(initializer, state, name);\n        }\n      }\n    },\n    AssignmentExpression(path, state) {\n      const id = path.node.left;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"right\"));\n        if (isAnonymous(initializer)) {\n          switch (path.node.operator) {\n            case \"=\":\n            case \"&&=\":\n            case \"||=\":\n            case \"??=\":\n              visitor(initializer, state, id.name);\n          }\n        }\n      }\n    },\n    AssignmentPattern(path, state) {\n      const id = path.node.left;\n      if (id.type === \"Identifier\") {\n        const initializer = skipTransparentExprWrappers(path.get(\"right\"));\n        if (isAnonymous(initializer)) {\n          const name = id.name;\n          visitor(initializer, state, name);\n        }\n      }\n    },\n    // We listen on ObjectExpression so that we don't have to visit\n    // the object properties under object patterns\n    ObjectExpression(path, state) {\n      for (const propertyPath of path.get(\"properties\")) {\n        const { node } = propertyPath;\n        if (node.type !== \"ObjectProperty\") continue;\n        const id = node.key;\n        const initializer = skipTransparentExprWrappers(\n          propertyPath.get(\"value\"),\n        );\n        if (isAnonymous(initializer)) {\n          if (!node.computed) {\n            // ******** RS: PropertyDefinitionEvaluation\n            if (!isProtoKey(id as t.StringLiteral | t.Identifier)) {\n              if (id.type === \"Identifier\") {\n                visitor(initializer, state, id.name);\n              } else {\n                const className = t.stringLiteral(\n                  (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                    .value + \"\",\n                );\n                visitor(initializer, state, className);\n              }\n            }\n          } else {\n            const ref = handleComputedProperty(\n              propertyPath as NodePath<t.ObjectProperty>,\n              // The key of a computed object property must not be a private name\n              id as t.Expression,\n              state,\n            );\n            visitor(initializer, state, ref);\n          }\n        }\n      }\n    },\n    ClassPrivateProperty(path, state) {\n      const { node } = path;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        const className = t.stringLiteral(\"#\" + node.key.id.name);\n        visitor(initializer, state, className);\n      }\n    },\n    ClassAccessorProperty(path, state) {\n      const { node } = path;\n      const id = node.key;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        if (!node.computed) {\n          if (id.type === \"Identifier\") {\n            visitor(initializer, state, id.name);\n          } else if (id.type === \"PrivateName\") {\n            const className = t.stringLiteral(\"#\" + id.id.name);\n            visitor(initializer, state, className);\n          } else {\n            const className = t.stringLiteral(\n              (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                .value + \"\",\n            );\n            visitor(initializer, state, className);\n          }\n        } else {\n          const ref = handleComputedProperty(\n            path,\n            // The key of a computed accessor property must not be a private name\n            id as t.Expression,\n            state,\n          );\n          visitor(initializer, state, ref);\n        }\n      }\n    },\n    ClassProperty(path, state) {\n      const { node } = path;\n      const id = node.key;\n      const initializer = skipTransparentExprWrappers(path.get(\"value\"));\n      if (isAnonymous(initializer)) {\n        if (!node.computed) {\n          if (id.type === \"Identifier\") {\n            visitor(initializer, state, id.name);\n          } else {\n            const className = t.stringLiteral(\n              (id as t.StringLiteral | t.NumericLiteral | t.BigIntLiteral)\n                .value + \"\",\n            );\n            visitor(initializer, state, className);\n          }\n        } else {\n          const ref = handleComputedProperty(path, id, state);\n          visitor(initializer, state, ref);\n        }\n      }\n    },\n  } satisfies Visitor<PluginPass>;\n}\n\nfunction isDecoratedAnonymousClassExpression(path: NodePath) {\n  return (\n    path.isClassExpression({ id: null }) && shouldTransformClass(path.node)\n  );\n}\n\nfunction generateLetUidIdentifier(scope: Scope, name: string) {\n  const id = scope.generateUidIdentifier(name);\n  scope.push({ id, kind: \"let\" });\n  return t.cloneNode(id);\n}\n\nexport default function (\n  { assertVersion, assumption }: PluginAPI,\n  { loose }: Options,\n  version: DecoratorVersionKind,\n  inherits: PluginObject[\"inherits\"],\n): PluginObject {\n  if (process.env.BABEL_8_BREAKING) {\n    assertVersion(REQUIRED_VERSION(\"^7.21.0\"));\n  } else {\n    if (\n      version === \"2023-11\" ||\n      version === \"2023-05\" ||\n      version === \"2023-01\"\n    ) {\n      assertVersion(REQUIRED_VERSION(\"^7.21.0\"));\n    } else if (version === \"2021-12\") {\n      assertVersion(REQUIRED_VERSION(\"^7.16.0\"));\n    } else {\n      assertVersion(REQUIRED_VERSION(\"^7.19.0\"));\n    }\n  }\n\n  const VISITED = new WeakSet<NodePath>();\n  const constantSuper = assumption(\"constantSuper\") ?? loose;\n  const ignoreFunctionLength = assumption(\"ignoreFunctionLength\") ?? loose;\n\n  const namedEvaluationVisitor: Visitor<PluginPass> =\n    NamedEvaluationVisitoryFactory(\n      isDecoratedAnonymousClassExpression,\n      visitClass,\n    );\n\n  function visitClass(\n    path: NodePath<t.Class>,\n    state: PluginPass,\n    className: string | t.Identifier | t.StringLiteral | undefined,\n  ) {\n    if (VISITED.has(path)) return;\n    const { node } = path;\n    className ??= node.id?.name;\n    const newPath = transformClass(\n      path,\n      state,\n      constantSuper,\n      ignoreFunctionLength,\n      className,\n      namedEvaluationVisitor,\n      version,\n    );\n    if (newPath) {\n      VISITED.add(newPath);\n      return;\n    }\n    VISITED.add(path);\n  }\n\n  return {\n    name: \"proposal-decorators\",\n    inherits: inherits,\n\n    visitor: {\n      ExportDefaultDeclaration(path, state) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          isDecorated(declaration)\n        ) {\n          const isAnonymous = !declaration.id;\n          const updatedVarDeclarationPath = splitExportDeclaration(\n            path,\n          ) as unknown as NodePath<t.ClassDeclaration>;\n          if (isAnonymous) {\n            visitClass(\n              updatedVarDeclarationPath,\n              state,\n              t.stringLiteral(\"default\"),\n            );\n          }\n        }\n      },\n      ExportNamedDeclaration(path) {\n        const { declaration } = path.node;\n        if (\n          declaration?.type === \"ClassDeclaration\" &&\n          // When compiling class decorators we need to replace the class\n          // binding, so we must split it in two separate declarations.\n          isDecorated(declaration)\n        ) {\n          splitExportDeclaration(path);\n        }\n      },\n\n      Class(path, state) {\n        visitClass(path, state, undefined);\n      },\n\n      ...namedEvaluationVisitor,\n    },\n  };\n}\n"], "mappings": ";;;;;;AACA,IAAAA,KAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,6BAAA,GAAAF,OAAA;AAGA,IAAAG,wCAAA,GAAAH,OAAA;AACA,IAAAI,OAAA,GAAAJ,OAAA;AAIA,IAAAK,KAAA,GAAAL,OAAA;AAiCA,SAASM,WAAWA,CAACC,EAAY,EAAEC,GAAG,GAAGD,EAAE,CAACE,MAAM,GAAG,CAAC,EAAQ;EAE5D,IAAID,GAAG,KAAK,CAAC,CAAC,EAAE;IACdD,EAAE,CAACG,OAAO,GAAqB,CAAC;IAChC;EACF;EAEA,MAAMC,OAAO,GAAGJ,EAAE,CAACC,GAAG,CAAC;EAEvB,IAAIG,OAAO,OAAyB,EAAE;IAEpCJ,EAAE,CAACC,GAAG,CAAC,KAAuB;EAChC,CAAC,MAAM,IAAIG,OAAO,QAAyB,EAAE;IAE3CJ,EAAE,CAACC,GAAG,CAAC,KAAuB;IAC9BF,WAAW,CAACC,EAAE,EAAEC,GAAG,GAAG,CAAC,CAAC;EAC1B,CAAC,MAAM;IAELD,EAAE,CAACC,GAAG,CAAC,GAAGG,OAAO,GAAG,CAAC;EACvB;AACF;AASA,SAASC,iCAAiCA,CACxCC,SAA2D,EACtC;EACrB,MAAMC,gBAA0B,GAAG,EAAE;EACrC,MAAMC,YAAY,GAAG,IAAIC,GAAG,CAAS,CAAC;EAEtCH,SAAS,CAACI,QAAQ,CAAC;IACjBC,WAAWA,CAACC,IAAI,EAAE;MAChBJ,YAAY,CAACK,GAAG,CAACD,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC;IACrC;EACF,CAAC,CAAC;EAEF,OAAO,MAAqB;IAC1B,IAAIC,SAAS;IACb,GAAG;MACDjB,WAAW,CAACQ,gBAAgB,CAAC;MAC7BS,SAAS,GAAGC,MAAM,CAACC,YAAY,CAAC,GAAGX,gBAAgB,CAAC;IACtD,CAAC,QAAQC,YAAY,CAACW,GAAG,CAACH,SAAS,CAAC;IAEpC,OAAOI,WAAC,CAACC,WAAW,CAACD,WAAC,CAACE,UAAU,CAACN,SAAS,CAAC,CAAC;EAC/C,CAAC;AACH;AAQA,SAASO,qCAAqCA,CAC5CjB,SAA2D,EACtC;EACrB,IAAIkB,SAA8B;EAElC,OAAO,MAAqB;IAC1B,IAAI,CAACA,SAAS,EAAE;MACdA,SAAS,GAAGnB,iCAAiC,CAACC,SAAS,CAAC;IAC1D;IAEA,OAAOkB,SAAS,CAAC,CAAC;EACpB,CAAC;AACH;AAUA,SAASC,mBAAmBA,CAC1Bb,IAAsD,EACtDc,SAA8D,EAI9D;EACA,MAAM1B,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACd,EAAE;EACvB,MAAM2B,KAAK,GAAGf,IAAI,CAACe,KAAK;EACxB,IAAIf,IAAI,CAACgB,IAAI,KAAK,kBAAkB,EAAE;IACpC,MAAMF,SAAS,GAAG1B,EAAE,CAACe,IAAI;IACzB,MAAMc,KAAK,GAAGF,KAAK,CAACG,gCAAgC,CAAC9B,EAAE,CAAC;IACxD,MAAM+B,OAAO,GAAGX,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC;IAEvCC,KAAK,CAACK,MAAM,CAACN,SAAS,EAAEG,KAAK,CAACd,IAAI,CAAC;IAEnCH,IAAI,CAACqB,GAAG,CAAC,IAAI,CAAC,CAACC,WAAW,CAACH,OAAO,CAAC;IAEnC,OAAO;MAAE/B,EAAE,EAAEoB,WAAC,CAACe,SAAS,CAACN,KAAK,CAAC;MAAEjB;IAAK,CAAC;EACzC,CAAC,MAAM;IACL,IAAIiB,KAAmB;IAEvB,IAAI7B,EAAE,EAAE;MACN0B,SAAS,GAAG1B,EAAE,CAACe,IAAI;MACnBc,KAAK,GAAGO,wBAAwB,CAACT,KAAK,CAACU,MAAM,EAAEX,SAAS,CAAC;MACzDC,KAAK,CAACK,MAAM,CAACN,SAAS,EAAEG,KAAK,CAACd,IAAI,CAAC;IACrC,CAAC,MAAM;MACLc,KAAK,GAAGO,wBAAwB,CAC9BT,KAAK,CAACU,MAAM,EACZ,OAAOX,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,iBAC9C,CAAC;IACH;IAEA,MAAMY,YAAY,GAAGlB,WAAC,CAACmB,eAAe,CACpC,OAAOb,SAAS,KAAK,QAAQ,GAAGN,WAAC,CAACE,UAAU,CAACI,SAAS,CAAC,GAAG,IAAI,EAC9Dd,IAAI,CAACE,IAAI,CAAC0B,UAAU,EACpB5B,IAAI,CAACE,IAAI,CAAC2B,IACZ,CAAC;IAED,MAAM,CAACC,OAAO,CAAC,GAAG9B,IAAI,CAACsB,WAAW,CAChCd,WAAC,CAACuB,kBAAkB,CAAC,CAACL,YAAY,EAAET,KAAK,CAAC,CAC5C,CAAC;IAED,OAAO;MACL7B,EAAE,EAAEoB,WAAC,CAACe,SAAS,CAACN,KAAK,CAAC;MACtBjB,IAAI,EAAE8B,OAAO,CAACT,GAAG,CAAC,eAAe;IACnC,CAAC;EACH;AACF;AAEA,SAASW,qBAAqBA,CAC5BC,GAAiC,EACjCC,KAA+B,EAC/BC,QAAiB,EACyB;EAC1C,IAAIF,GAAG,CAACjB,IAAI,KAAK,aAAa,EAAE;IAC9B,OAAOR,WAAC,CAAC4B,oBAAoB,CAACH,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEF,QAAQ,CAAC;EAChE,CAAC,MAAM;IACL,OAAO3B,WAAC,CAAC8B,aAAa,CAACL,GAAG,EAAEC,KAAK,EAAEG,SAAS,EAAEA,SAAS,EAAEF,QAAQ,CAAC;EACpE;AACF;AAEA,SAASI,oBAAoBA,CAC3BzB,SAAuB,EACvB0B,OAA0C,EAC1CC,SAAuC,EACvCC,SAAuC,EACvCC,SAAwB,EACxBC,UAAmB,EACnBT,QAAiB,EACjBU,OAA6B,EACvB;EACN,MAAMC,OAAO,GACX,CAACD,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDV,QAAQ,GACJrB,SAAS,GACTN,WAAC,CAACuC,cAAc,CAAC,CAAC;EAExB,MAAMC,UAAU,GAAGxC,WAAC,CAACyC,cAAc,CAAC,CAClCzC,WAAC,CAAC0C,eAAe,CACf1C,WAAC,CAAC2C,gBAAgB,CAAC3C,WAAC,CAACe,SAAS,CAACuB,OAAO,CAAC,EAAEtC,WAAC,CAACe,SAAS,CAACoB,SAAS,CAAC,CACjE,CAAC,CACF,CAAC;EAEF,MAAMS,UAAU,GAAG5C,WAAC,CAACyC,cAAc,CAAC,CAClCzC,WAAC,CAAC6C,mBAAmB,CACnB7C,WAAC,CAAC8C,oBAAoB,CACpB,GAAG,EACH9C,WAAC,CAAC2C,gBAAgB,CAAC3C,WAAC,CAACe,SAAS,CAACuB,OAAO,CAAC,EAAEtC,WAAC,CAACe,SAAS,CAACoB,SAAS,CAAC,CAAC,EAChEnC,WAAC,CAACE,UAAU,CAAC,GAAG,CAClB,CACF,CAAC,CACF,CAAC;EAEF,IAAI6C,MAA4C,EAC9CC,MAA4C;EAE9C,IAAIf,SAAS,CAACzB,IAAI,KAAK,aAAa,EAAE;IACpCuC,MAAM,GAAG/C,WAAC,CAACiD,kBAAkB,CAAC,KAAK,EAAEhB,SAAS,EAAE,EAAE,EAAEO,UAAU,EAAEb,QAAQ,CAAC;IACzEqB,MAAM,GAAGhD,WAAC,CAACiD,kBAAkB,CAC3B,KAAK,EACLf,SAAS,EACT,CAAClC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnB0C,UAAU,EACVjB,QACF,CAAC;EACH,CAAC,MAAM;IACLoB,MAAM,GAAG/C,WAAC,CAACkD,WAAW,CACpB,KAAK,EACLjB,SAAS,EACT,EAAE,EACFO,UAAU,EACVJ,UAAU,EACVT,QACF,CAAC;IACDqB,MAAM,GAAGhD,WAAC,CAACkD,WAAW,CACpB,KAAK,EACLhB,SAAS,EACT,CAAClC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnB0C,UAAU,EACVR,UAAU,EACVT,QACF,CAAC;EACH;EAEAK,OAAO,CAACmB,WAAW,CAACH,MAAM,CAAC;EAC3BhB,OAAO,CAACmB,WAAW,CAACJ,MAAM,CAAC;AAC7B;AAEA,SAASK,wBAAwBA,CAC/BjB,SAAwB,EACxBE,OAA6B,EACyB;EACtD,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,IAAIA,OAAO,KAAK,SAAS,EAAE;IAC3E,OAAO,CACLgB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC9B;AACA,wBAAwBvD,WAAC,CAACe,SAAS,CAACoB,SAAS,CAAE;AAC/C;AACA,OAAO,EACDkB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC9B;AACA,iBAAiBvD,WAAC,CAACe,SAAS,CAACoB,SAAS,CAAE;AACxC;AACA,OAAO,CACF;EACH;EACA,OAAO,CACLkB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC5B,eAAevD,WAAC,CAACe,SAAS,CAACoB,SAAS,CAAE;AACtC,KAAK,EACDkB,cAAQ,CAACC,UAAU,CAACC,GAAI;AAC5B,oBAAoBvD,WAAC,CAACe,SAAS,CAACoB,SAAS,CAAE;AAC3C,KAAK,CACF;AACH;AAWA,SAASqB,yBAAyBA,CAChChE,IAA4B,EACJ;EACxBA,IAAI,GAAG,IAAAiE,oEAA2B,EAACjE,IAAI,CAAC;EACxC,IAAIA,IAAI,CAACkE,oBAAoB,CAAC,CAAC,EAAE;IAC/B,MAAMC,WAAW,GAAGnE,IAAI,CAACqB,GAAG,CAAC,aAAa,CAAC;IAC3C,OAAO2C,yBAAyB,CAACG,WAAW,CAACA,WAAW,CAAC7E,MAAM,GAAG,CAAC,CAAC,CAAC;EACvE;EACA,OAAOU,IAAI;AACb;AAYA,SAASoE,sBAAsBA,CAACpE,IAA4B,EAAgB;EAC1E,MAAMwC,OAAO,GAAGwB,yBAAyB,CAAChE,IAAI,CAAC;EAC/C,IAAIwC,OAAO,CAAC6B,oBAAoB,CAAC,CAAC,EAAE;IAClC,OAAO7D,WAAC,CAACe,SAAS,CAACvB,IAAI,CAACE,IAAI,CAAC;EAC/B,CAAC,MAAM,IAAIsC,OAAO,CAAC8B,YAAY,CAAC,CAAC,IAAItE,IAAI,CAACe,KAAK,CAACwD,MAAM,CAAC/B,OAAO,CAACtC,IAAI,CAACC,IAAI,CAAC,EAAE;IACzE,OAAOK,WAAC,CAACe,SAAS,CAACvB,IAAI,CAACE,IAAI,CAAC;EAC/B,CAAC,MAAM,IACLsC,OAAO,CAACgC,sBAAsB,CAAC,CAAC,IAChChC,OAAO,CAACnB,GAAG,CAAC,MAAM,CAAC,CAACiD,YAAY,CAAC,CAAC,EAClC;IACA,OAAO9D,WAAC,CAACe,SAAS,CAACiB,OAAO,CAACtC,IAAI,CAACuE,IAAoB,CAAC;EACvD,CAAC,MAAM;IACL,MAAM,IAAIC,KAAK,CACZ,oCAAmC1E,IAAI,CAAC2E,QAAQ,CAAC,CAAE,6BACtD,CAAC;EACH;AACF;AAaA,SAASC,+BAA+BA,CACtCT,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAM5C,GAAG,GAAG4C,SAAS,CAACxD,GAAG,CAAC,KAAK,CAA2B;EAC1D,IAAIY,GAAG,CAACiC,oBAAoB,CAAC,CAAC,EAAE;IAC9BC,WAAW,CAACW,IAAI,CAAC,GAAG7C,GAAG,CAAC/B,IAAI,CAACiE,WAAW,CAAC;EAC3C,CAAC,MAAM;IACLA,WAAW,CAACW,IAAI,CAAC7C,GAAG,CAAC/B,IAAI,CAAC;EAC5B;EACA+B,GAAG,CAACX,WAAW,CAACyD,uBAAuB,CAACZ,WAAW,CAAC,CAAC;AACvD;AAcA,SAASa,8BAA8BA,CACrCb,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAM5C,GAAG,GAAG4C,SAAS,CAACxD,GAAG,CAAC,KAAK,CAA2B;EAC1D,MAAM4D,UAAU,GAAGjB,yBAAyB,CAAC/B,GAAG,CAAC;EACjD,IAAIgD,UAAU,CAACZ,oBAAoB,CAAC,CAAC,EAAE;IACrCO,+BAA+B,CAACT,WAAW,EAAEU,SAAS,CAAC;EACzD,CAAC,MAAM;IACL,MAAMK,WAAW,GAAGjD,GAAG,CAAClB,KAAK,CAACU,MAAM;IACpC,MAAM0D,eAAe,GAAG,IAAAC,wBAAkB,EACxCH,UAAU,CAAC/E,IAAI,EACfgF,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;IACD,IAAI,CAACF,eAAe,EAAE;MAGpBP,+BAA+B,CAACT,WAAW,EAAEU,SAAS,CAAC;IACzD,CAAC,MAAM;MACL,MAAMS,kBAAkB,GAAG,CACzB,GAAGnB,WAAW,EAEd3D,WAAC,CAACe,SAAS,CAAC4D,eAAe,CAACV,IAAI,CAAC,CAClC;MACD,MAAMc,gBAAgB,GAAGN,UAAU,CAACO,UAAU;MAC9C,IAAID,gBAAgB,CAACrB,oBAAoB,CAAC,CAAC,EAAE;QAC3CqB,gBAAgB,CAACE,aAAa,CAAC,aAAa,EAAEH,kBAAkB,CAAC;MACnE,CAAC,MAAM;QACLL,UAAU,CAAC3D,WAAW,CACpByD,uBAAuB,CAAC,CACtBvE,WAAC,CAACe,SAAS,CAAC4D,eAAe,CAAC,EAC5B,GAAGG,kBAAkB,CACtB,CACH,CAAC;MACH;IACF;EACF;AACF;AAWA,SAASI,oCAAoCA,CAC3CvB,WAA2B,EAC3BU,SAEC,EACD;EACA,MAAMc,WAAW,GAAGd,SAAS,CAACxD,GAAG,CAAC,OAAO,CAAC;EAC1C,IAAIsE,WAAW,CAACzF,IAAI,EAAE;IACpBiE,WAAW,CAACW,IAAI,CAACa,WAAW,CAACzF,IAAI,CAAC;EACpC,CAAC,MAAM,IAAIiE,WAAW,CAAC7E,MAAM,GAAG,CAAC,EAAE;IACjC6E,WAAW,CAACA,WAAW,CAAC7E,MAAM,GAAG,CAAC,CAAC,GAAGkB,WAAC,CAACoF,eAAe,CACrD,MAAM,EACNzB,WAAW,CAACA,WAAW,CAAC7E,MAAM,GAAG,CAAC,CACpC,CAAC;EACH;EACAqG,WAAW,CAACrE,WAAW,CAACyD,uBAAuB,CAACZ,WAAW,CAAC,CAAC;AAC/D;AAEA,SAAS0B,+BAA+BA,CACtC1B,WAA2B,EAC3B2B,SAAkC,EAClC;EACAA,SAAS,CAACC,gBAAgB,CACxB,MAAM,EACNvF,WAAC,CAAC6C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAC5D,CAAC;AACH;AAEA,SAAS6B,+BAA+BA,CACtC7B,WAA2B,EAC3B8B,eAAwC,EACxC;EACAA,eAAe,CAAC/F,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACpCiB,WAAC,CAAC6C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAC5D,CAAC;AACH;AAEA,SAAS+B,yBAAyBA,CAChCpC,UAAwB,EACxBqC,aAA2B,EAC3B;EACA,OACE3F,WAAC,CAAC4F,gBAAgB,CAACtC,UAAU,CAAC,IAC9BtD,WAAC,CAAC8D,YAAY,CAACR,UAAU,CAACuC,MAAM,EAAE;IAAElG,IAAI,EAAEgG,aAAa,CAAChG;EAAK,CAAC,CAAC;AAEnE;AASA,SAASmG,+BAA+BA,CACtCnC,WAA2B,EAC3BoC,cAA4B,EAC5B;EAEA,IACEpC,WAAW,CAAC7E,MAAM,IAAI,CAAC,IACvB4G,yBAAyB,CAAC/B,WAAW,CAAC,CAAC,CAAC,EAAEoC,cAAc,CAAC,EACzD;IACA,MAAMC,eAAe,GAAGhG,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACe,SAAS,CAACgF,cAAc,CAAC,EAAE,CACpEpC,WAAW,CAAC,CAAC,CAAC,CACf,CAAC;IACFA,WAAW,CAACuC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAEF,eAAe,CAAC;EAC3C;EAEA,IACErC,WAAW,CAAC7E,MAAM,IAAI,CAAC,IACvBkB,WAAC,CAACmG,gBAAgB,CAACxC,WAAW,CAACA,WAAW,CAAC7E,MAAM,GAAG,CAAC,CAAC,CAAC,IACvD4G,yBAAyB,CACvB/B,WAAW,CAACA,WAAW,CAAC7E,MAAM,GAAG,CAAC,CAAC,EACnCiH,cACF,CAAC,EACD;IACApC,WAAW,CAACuC,MAAM,CAACvC,WAAW,CAAC7E,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;EAC/C;EACA,OAAOyF,uBAAuB,CAACZ,WAAW,CAAC;AAC7C;AAWA,SAASyC,0CAA0CA,CACjDzC,WAA2B,EAC3B8B,eAAwC,EACxCM,cAA4B,EAC5B;EACAN,eAAe,CAACnG,QAAQ,CAAC;IACvB+G,cAAc,EAAE;MACdC,IAAIA,CAAC9G,IAAI,EAAE;QACT,IAAI,CAACA,IAAI,CAACqB,GAAG,CAAC,QAAQ,CAAC,CAAC0F,OAAO,CAAC,CAAC,EAAE;QACnC,MAAMC,QAAQ,GAAG,CACfhH,IAAI,CAACE,IAAI,EACT,GAAGiE,WAAW,CAAC8C,GAAG,CAACC,IAAI,IAAI1G,WAAC,CAACe,SAAS,CAAC2F,IAAI,CAAC,CAAC,CAC9C;QAED,IAAIlH,IAAI,CAACmH,kBAAkB,CAAC,CAAC,EAAE;UAC7BH,QAAQ,CAAClC,IAAI,CAACtE,WAAC,CAACuC,cAAc,CAAC,CAAC,CAAC;QACnC;QACA/C,IAAI,CAACsB,WAAW,CACdgF,+BAA+B,CAACU,QAAQ,EAAET,cAAc,CAC1D,CAAC;QAEDvG,IAAI,CAACoH,IAAI,CAAC,CAAC;MACb;IACF,CAAC;IACDC,WAAWA,CAACrH,IAAI,EAAE;MAChB,IAAIA,IAAI,CAACE,IAAI,CAACoH,IAAI,KAAK,aAAa,EAAE;QACpCtH,IAAI,CAACoH,IAAI,CAAC,CAAC;MACb;IACF;EACF,CAAC,CAAC;AACJ;AAWA,SAASG,gCAAgCA,CACvCpD,WAA2B,EAC3BqD,cAAuB,EACvB;EACA,MAAM3F,IAAmB,GAAG,CAC1BrB,WAAC,CAAC6C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAAC,CAC5D;EACD,IAAIqD,cAAc,EAAE;IAClB3F,IAAI,CAACtC,OAAO,CACViB,WAAC,CAAC6C,mBAAmB,CACnB7C,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACiH,KAAK,CAAC,CAAC,EAAE,CAACjH,WAAC,CAACkH,aAAa,CAAClH,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,CACrE,CACF,CAAC;EACH;EACA,OAAOF,WAAC,CAACkD,WAAW,CAClB,aAAa,EACblD,WAAC,CAACE,UAAU,CAAC,aAAa,CAAC,EAC3B8G,cAAc,GAAG,CAAChH,WAAC,CAACmH,WAAW,CAACnH,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC,GAAG,EAAE,EAC3DF,WAAC,CAACyC,cAAc,CAACpB,IAAI,CACvB,CAAC;AACH;AAEA,SAAS+F,gCAAgCA,CAACzD,WAA2B,EAAE;EACrE,OAAO3D,WAAC,CAACqH,WAAW,CAAC,CACnBrH,WAAC,CAAC6C,mBAAmB,CAAC0B,uBAAuB,CAACZ,WAAW,CAAC,CAAC,CAC5D,CAAC;AACJ;AAGA,MAAM2D,KAAK,GAAG,CAAC;AACf,MAAMC,QAAQ,GAAG,CAAC;AAClB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,MAAM,GAAG,CAAC;AAEhB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,MAAM,GAAG,CAAC;AAChB,MAAMC,oBAAoB,GAAG,EAAE;AAE/B,SAASC,cAAcA,CAAC9F,OAA0C,EAAU;EAC1E,QAAQA,OAAO,CAACtC,IAAI,CAACc,IAAI;IACvB,KAAK,eAAe;IACpB,KAAK,sBAAsB;MACzB,OAAO8G,KAAK;IACd,KAAK,uBAAuB;MAC1B,OAAOC,QAAQ;IACjB,KAAK,aAAa;IAClB,KAAK,oBAAoB;MACvB,IAAIvF,OAAO,CAACtC,IAAI,CAACoH,IAAI,KAAK,KAAK,EAAE;QAC/B,OAAOW,MAAM;MACf,CAAC,MAAM,IAAIzF,OAAO,CAACtC,IAAI,CAACoH,IAAI,KAAK,KAAK,EAAE;QACtC,OAAOY,MAAM;MACf,CAAC,MAAM;QACL,OAAOF,MAAM;MACf;EACJ;AACF;AAmCA,SAASO,qBAAqBA,CAACC,IAAqB,EAAmB;EACrE,OAAO,CACL,GAAGA,IAAI,CAACC,MAAM,CACZC,EAAE,IAAIA,EAAE,CAACvG,QAAQ,IAAIuG,EAAE,CAACpB,IAAI,IAAIS,QAAQ,IAAIW,EAAE,CAACpB,IAAI,IAAIY,MACzD,CAAC,EACD,GAAGM,IAAI,CAACC,MAAM,CACZC,EAAE,IAAI,CAACA,EAAE,CAACvG,QAAQ,IAAIuG,EAAE,CAACpB,IAAI,IAAIS,QAAQ,IAAIW,EAAE,CAACpB,IAAI,IAAIY,MAC1D,CAAC,EACD,GAAGM,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,CAACvG,QAAQ,IAAIuG,EAAE,CAACpB,IAAI,KAAKQ,KAAK,CAAC,EACtD,GAAGU,IAAI,CAACC,MAAM,CAACC,EAAE,IAAI,CAACA,EAAE,CAACvG,QAAQ,IAAIuG,EAAE,CAACpB,IAAI,KAAKQ,KAAK,CAAC,CACxD;AACH;AAgBA,SAASa,sBAAsBA,CAC7BC,UAA0B,EAC1BC,cAA4C,EAC5ChG,OAA6B,EACC;EAC9B,MAAMiG,SAAS,GAAGF,UAAU,CAACtJ,MAAM;EACnC,MAAMyJ,WAAW,GAAGF,cAAc,CAACG,IAAI,CAACC,OAAO,CAAC;EAChD,MAAMC,IAAoB,GAAG,EAAE;EAC/B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,SAAS,EAAEK,CAAC,EAAE,EAAE;IAClC,KACGtG,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDkG,WAAW,EACX;MACAG,IAAI,CAACpE,IAAI,CACP+D,cAAc,CAACM,CAAC,CAAC,IAAI3I,WAAC,CAACoF,eAAe,CAAC,MAAM,EAAEpF,WAAC,CAAC4I,cAAc,CAAC,CAAC,CAAC,CACpE,CAAC;IACH;IACAF,IAAI,CAACpE,IAAI,CAAC8D,UAAU,CAACO,CAAC,CAAC,CAAC;EAC1B;EAEA,OAAO;IAAEE,QAAQ,EAAEN,WAAW;IAAEG;EAAK,CAAC;AACxC;AAEA,SAASI,uBAAuBA,CAC9BC,cAA+B,EAC/B1G,OAA6B,EACV;EACnB,OAAOrC,WAAC,CAACgJ,eAAe,CACtBD,cAAc,CAACtC,GAAG,CAACyB,EAAE,IAAI;IACvB,IAAIe,IAAI,GAAGf,EAAE,CAACpB,IAAI;IAClB,IAAIoB,EAAE,CAACvG,QAAQ,EAAE;MACfsH,IAAI,IACF5G,OAAO,KAAK,SAAS,IACaA,OAAO,KAAK,SAAS,GACnDuF,MAAM,GACND,kBAAkB;IAC1B;IACA,IAAIO,EAAE,CAACgB,kBAAkB,EAAED,IAAI,IAAIpB,oBAAoB;IAEvD,OAAO7H,WAAC,CAACgJ,eAAe,CAAC,CACvBd,EAAE,CAACiB,eAAe,EAClBnJ,WAAC,CAAC4I,cAAc,CAACK,IAAI,CAAC,EACtBf,EAAE,CAACvI,IAAI,EACP,IAAIuI,EAAE,CAACkB,cAAc,IAAI,EAAE,CAAC,CAC7B,CAAC;EACJ,CAAC,CACH,CAAC;AACH;AAEA,SAASC,8BAA8BA,CAACN,cAA+B,EAAE;EACvE,MAAMO,QAAwB,GAAG,EAAE;EAEnC,KAAK,MAAMpB,EAAE,IAAIa,cAAc,EAAE;IAC/B,MAAM;MAAEQ;IAAO,CAAC,GAAGrB,EAAE;IAErB,IAAIsB,KAAK,CAACC,OAAO,CAACF,MAAM,CAAC,EAAE;MACzBD,QAAQ,CAAChF,IAAI,CAAC,GAAGiF,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAIA,MAAM,KAAK1H,SAAS,EAAE;MAC/ByH,QAAQ,CAAChF,IAAI,CAACiF,MAAM,CAAC;IACvB;EACF;EAEA,OAAOD,QAAQ;AACjB;AAEA,SAASI,mBAAmBA,CAC1BrH,OAA6B,EAC7BL,OAAiB,EACjBP,GAAkB,EAClBkI,KAAmB,EACnBC,KAAmB,EACnBjI,QAAiB,EACjB;EACAK,OAAO,CAACmB,WAAW,CACjBnD,WAAC,CAACiD,kBAAkB,CAClB,KAAK,EACLjD,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChB,EAAE,EACFzB,WAAC,CAACyC,cAAc,CAAC,CACfzC,WAAC,CAAC0C,eAAe,CACf1C,WAAC,CAACiG,cAAc,CACdjG,WAAC,CAACe,SAAS,CAAC4I,KAAK,CAAC,EACetH,OAAO,KAAK,SAAS,IAAKV,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACuC,cAAc,CAAC,CAAC,CACzB,CACF,CAAC,CACF,CAAC,EACFZ,QACF,CACF,CAAC;EAEDK,OAAO,CAACmB,WAAW,CACjBnD,WAAC,CAACiD,kBAAkB,CAClB,KAAK,EACLjD,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChB,CAACzB,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAACyC,cAAc,CAAC,CACfzC,WAAC,CAAC6C,mBAAmB,CACnB7C,WAAC,CAACiG,cAAc,CACdjG,WAAC,CAACe,SAAS,CAAC6I,KAAK,CAAC,EACevH,OAAO,KAAK,SAAS,IAAKV,QAAQ,GAC/D,CAAC3B,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,GACnB,CAACF,WAAC,CAACuC,cAAc,CAAC,CAAC,EAAEvC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAC5C,CACF,CAAC,CACF,CAAC,EACFyB,QACF,CACF,CAAC;AACH;AAEA,SAASkI,mBAAmBA,CAC1B7H,OAAuC,EACvCP,GAAkB,EAClBqI,cAA4B,EAC5BnI,QAAiB,EACjB;EACA,IAAIoI,MAAwC;EAC5C,IAAIC,KAAoB;EAExB,IAAIhI,OAAO,CAACtC,IAAI,CAACoH,IAAI,KAAK,KAAK,EAAE;IAC/BiD,MAAM,GAAG,CAAC/J,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC;IAC5B8J,KAAK,GAAG,CACNhK,WAAC,CAAC6C,mBAAmB,CACnB7C,WAAC,CAACiG,cAAc,CAAC6D,cAAc,EAAE,CAC/B9J,WAAC,CAACuC,cAAc,CAAC,CAAC,EAClBvC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAClB,CACH,CAAC,CACF;EACH,CAAC,MAAM;IACL6J,MAAM,GAAG,EAAE;IACXC,KAAK,GAAG,CACNhK,WAAC,CAAC0C,eAAe,CAAC1C,WAAC,CAACiG,cAAc,CAAC6D,cAAc,EAAE,CAAC9J,WAAC,CAACuC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC,CAC1E;EACH;EAEAP,OAAO,CAAClB,WAAW,CACjBd,WAAC,CAACiD,kBAAkB,CAClBjB,OAAO,CAACtC,IAAI,CAACoH,IAAI,EACjB9G,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChBsI,MAAM,EACN/J,WAAC,CAACyC,cAAc,CAACuH,KAAK,CAAC,EACvBrI,QACF,CACF,CAAC;AACH;AAEA,SAASsI,6BAA6BA,CACpCzK,IAA4B,EACe;EAC3C,MAAM;IAAEgB;EAAK,CAAC,GAAGhB,IAAI;EAErB,OACEgB,IAAI,KAAK,iBAAiB,IAC1BA,IAAI,KAAK,kBAAkB,IAC3BA,IAAI,KAAK,aAAa;AAE1B;AAEA,SAAS0J,iBAAiBA,CAACF,KAAoB,EAAE;EAC/C,OAAOhK,WAAC,CAACiG,cAAc,CACrBjG,WAAC,CAACmK,uBAAuB,CAAC,EAAE,EAAEnK,WAAC,CAACyC,cAAc,CAACuH,KAAK,CAAC3I,IAAI,CAAC,CAAC,EAC3D,EACF,CAAC;AACH;AAEA,SAAS+I,4BAA4BA,CAACJ,KAAoB,EAAE;EAC1D,OAAOhK,WAAC,CAACqK,kBAAkB,CAAC,IAAI,EAAE,EAAE,EAAErK,WAAC,CAACyC,cAAc,CAACuH,KAAK,CAAC3I,IAAI,CAAC,CAAC;AACrE;AAEA,SAASiJ,yBAAyBA,CAAC5I,KAAmB,EAAE;EACtD,OAAO1B,WAAC,CAACqK,kBAAkB,CACzB,IAAI,EACJ,EAAE,EACFrK,WAAC,CAACyC,cAAc,CAAC,CAACzC,WAAC,CAAC0C,eAAe,CAAChB,KAAK,CAAC,CAAC,CAC7C,CAAC;AACH;AAEA,SAAS6C,uBAAuBA,CAACgG,KAAqB,EAAE;EACtD,IAAIA,KAAK,CAACzL,MAAM,KAAK,CAAC,EAAE,OAAOkB,WAAC,CAACoF,eAAe,CAAC,MAAM,EAAEpF,WAAC,CAAC4I,cAAc,CAAC,CAAC,CAAC,CAAC;EAC7E,IAAI2B,KAAK,CAACzL,MAAM,KAAK,CAAC,EAAE,OAAOyL,KAAK,CAAC,CAAC,CAAC;EACvC,OAAOvK,WAAC,CAACuB,kBAAkB,CAACgJ,KAAK,CAAC;AACpC;AASA,SAASC,yCAAyCA,CAAC9K,IAA0B,EAAE;EAC7E,MAAM;IAAEqK,MAAM;IAAE1I,IAAI;IAAEjB,SAAS,EAAEqK,WAAW;IAAEC,KAAK,EAAEC;EAAQ,CAAC,GAAGjL,IAAI;EACrE,OAAOM,WAAC,CAACqK,kBAAkB,CACzBxI,SAAS,EAETkI,MAAM,EACN1I,IAAI,EACJoJ,WAAW,EACXE,OACF,CAAC;AACH;AAEA,SAASC,yBAAyBA,CAChCC,KAAiB,EACjBvK,SAAyC,EACzC;EACA,OAAON,WAAC,CAACiG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,iBAAiB,CAAC,EAAE,CAC1D9K,WAAC,CAACuC,cAAc,CAAC,CAAC,EAClBjC,SAAS,CACV,CAAC;AACJ;AAEA,SAASyK,uBAAuBA,CAACF,KAAiB,EAAEG,WAAyB,EAAE;EAC7E,OAAOhL,WAAC,CAACiG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE,CAACE,WAAW,CAAC,CAAC;AAC1E;AAEA,SAASC,8BAA8BA,CAACC,SAAwB,EAAE;EAChE,OAAOlL,WAAC,CAACmK,uBAAuB,CAC9B,CAACnK,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAAC,EACnBF,WAAC,CAACmL,gBAAgB,CAAC,IAAI,EAAEnL,WAAC,CAACe,SAAS,CAACmK,SAAS,CAAC,EAAElL,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CACpE,CAAC;AACH;AAKA,SAASkL,+BAA+BA,CAAC9H,UAAkB,EAAE;EAC3D,IAAI;IACFtD,WAAC,CAACqL,YAAY,CAAC/H,UAAU,EAAE5D,IAAI,IAAI;MACjC,IACEM,WAAC,CAACmG,gBAAgB,CAACzG,IAAI,CAAC,IACxBM,WAAC,CAACuG,OAAO,CAAC7G,IAAI,CAAC,IACfM,WAAC,CAACsL,iBAAiB,CAAC5L,IAAI,CAAC,IACzBM,WAAC,CAACuL,iBAAiB,CAAC7L,IAAI,CAAC,IACzBM,WAAC,CAAC8D,YAAY,CAACpE,IAAI,EAAE;QAAEC,IAAI,EAAE;MAAY,CAAC,CAAC,IAC1CK,WAAC,CAACwL,cAAc,CAAC9L,IAAI,CAAC,IAAIA,IAAI,CAAC+L,IAAI,CAAC9L,IAAI,KAAK,QAAS,EACvD;QAEA,MAAM,IAAI;MACZ;IACF,CAAC,CAAC;IACF,OAAO,KAAK;EACd,CAAC,CAAC,OAAA+L,OAAA,EAAM;IACN,OAAO,IAAI;EACb;AACF;AAEA,SAASC,gBAAgBA,CAACrI,UAAkB,EAAE;EAC5C,IAAI;IACFtD,WAAC,CAACqL,YAAY,CAAC/H,UAAU,EAAE5D,IAAI,IAAI;MACjC,IAAIM,WAAC,CAAC4L,aAAa,CAAClM,IAAI,CAAC,EAAE;QAEzB,MAAM,IAAI;MACZ;IACF,CAAC,CAAC;IACF,OAAO,KAAK;EACd,CAAC,CAAC,OAAAmM,QAAA,EAAM;IACN,OAAO,IAAI;EACb;AACF;AAUA,SAASC,oBAAoBA,CAACtM,IAA+C,EAAE;EAC7E,MAAM;IAAEE;EAAK,CAAC,GAAGF,IAAI;EACrBE,IAAI,CAACqM,QAAQ,GAAG,IAAI;EACpB,IAAI/L,WAAC,CAAC8D,YAAY,CAACpE,IAAI,CAAC+B,GAAG,CAAC,EAAE;IAC5B/B,IAAI,CAAC+B,GAAG,GAAGzB,WAAC,CAACgM,aAAa,CAACtM,IAAI,CAAC+B,GAAG,CAAC9B,IAAI,CAAC;EAC3C;AACF;AAEA,SAASsM,wBAAwBA,CAACzM,IAAc,EAAEJ,YAAsB,EAAE;EACxE,IAAI8M,6BAA6B,GAAG,KAAK;EACzC,IAAI9M,YAAY,CAACN,MAAM,GAAG,CAAC,EAAE;IAC3B,MAAMqN,kBAAkB,GAAG,IAAAC,iCAAyB,EAGlD;MACA7M,WAAWA,CAACC,IAAI,EAAEqL,KAAK,EAAE;QACvB,IAAIA,KAAK,CAACwB,eAAe,CAACtM,GAAG,CAACP,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC,EAAE;UAChDuM,6BAA6B,GAAG,IAAI;UACpC1M,IAAI,CAAC8M,IAAI,CAAC,CAAC;QACb;MACF;IACF,CAAC,CAAC;IACF,MAAMD,eAAe,GAAG,IAAIE,GAAG,CAAe,CAAC;IAC/C,KAAK,MAAM5M,IAAI,IAAIP,YAAY,EAAE;MAC/BiN,eAAe,CAACG,GAAG,CAAC7M,IAAI,EAAE,IAAI,CAAC;IACjC;IACAH,IAAI,CAACF,QAAQ,CAAC6M,kBAAkB,EAAE;MAChCE,eAAe,EAAEA;IACnB,CAAC,CAAC;EACJ;EACA,OAAOH,6BAA6B;AACtC;AAEA,SAASO,6BAA6BA,CACpCjN,IAAuB,EACvBkN,uBAAoC,EACpC;EACA,MAAMP,kBAAkB,GAAG,IAAAC,iCAAyB,EAGlD;IACA7M,WAAWA,CAACC,IAAI,EAAEqL,KAAK,EAAE;MACvB,IAAI,CAACA,KAAK,CAACwB,eAAe,CAACtM,GAAG,CAACP,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAI,CAAC,EAAE;MAEnD,MAAMqF,UAAU,GAAGxF,IAAI,CAACwF,UAAU;MAClC,MAAM2H,gBAAgB,GAAG3H,UAAU,CAACA,UAAU;MAE9C,IAEG2H,gBAAgB,CAACjN,IAAI,CAACc,IAAI,KAAK,sBAAsB,IACpDmM,gBAAgB,CAACjN,IAAI,CAACuE,IAAI,KAAKe,UAAU,CAACtF,IAAI,IAEhDiN,gBAAgB,CAACjN,IAAI,CAACc,IAAI,KAAK,kBAAkB,IAEjDmM,gBAAgB,CAACjN,IAAI,CAACc,IAAI,KAAK,aAAa,IAE5CmM,gBAAgB,CAACjN,IAAI,CAACc,IAAI,KAAK,cAAc,IAE5CmM,gBAAgB,CAACjN,IAAI,CAACc,IAAI,KAAK,gBAAgB,IAC9CmM,gBAAgB,CAACjN,IAAI,CAACgC,KAAK,KAAKsD,UAAU,CAACtF,IAAI,IAC/CiN,gBAAgB,CAAC3H,UAAU,CAACxE,IAAI,KAAK,eAAgB,IAEtDmM,gBAAgB,CAACjN,IAAI,CAACc,IAAI,KAAK,gBAAgB,IAC9CmM,gBAAgB,CAACjN,IAAI,CAACuE,IAAI,KAAKe,UAAU,CAACtF,IAAK,EACjD;QACA,MAAMF,IAAI,CAACoN,mBAAmB,CAC3B,kDAAiDpN,IAAI,CAACE,IAAI,CAACd,EAAE,CAACe,IAAK,mCACtE,CAAC;MACH;IACF;EACF,CAAC,CAAC;EACF,MAAM0M,eAAe,GAAG,IAAIE,GAAG,CAAe,CAAC;EAC/C,KAAK,MAAM5M,IAAI,IAAI+M,uBAAuB,EAAE;IAC1CL,eAAe,CAACG,GAAG,CAAC7M,IAAI,EAAE,IAAI,CAAC;EACjC;EACAH,IAAI,CAACF,QAAQ,CAAC6M,kBAAkB,EAAE;IAChCE,eAAe,EAAEA;EACnB,CAAC,CAAC;AACJ;AAEA,SAASQ,cAAcA,CACrBrN,IAAuB,EACvBqL,KAAiB,EACjBiC,aAAsB,EACtBC,oBAA6B,EAC7BzM,SAA8D,EAC9D0M,eAAoC,EACpC3K,OAA6B,EACnB;EAAA,IAAA4K,mBAAA;EACV,MAAM5L,IAAI,GAAG7B,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC;EAElC,MAAMqM,eAAe,GAAG1N,IAAI,CAACE,IAAI,CAAC0I,UAAU;EAC5C,IAAI+E,oBAAoB,GAAG,KAAK;EAChC,IAAIC,0BAA0B,GAAG,KAAK;EACtC,IAAIC,oBAAoB,GAAG,KAAK;EAEhC,MAAMC,uBAAuB,GAAGnN,qCAAqC,CAACX,IAAI,CAAC;EAE3E,MAAM+N,gBAA0C,GAAG,EAAE;EACrD,MAAM7I,WAAkB,GAAGlF,IAAI,CAACe,KAAK,CAACU,MAAM;EAC5C,MAAMuM,iBAAiB,GAAGA,CACxBlK,UAAwB,EACxBmK,IAAY,EACZC,WAAqC,KAClC;IACH,MAAMC,gBAAgB,GAAG3M,wBAAwB,CAAC0D,WAAW,EAAE+I,IAAI,CAAC;IACpEC,WAAW,CAACpJ,IAAI,CAACtE,WAAC,CAAC8C,oBAAoB,CAAC,GAAG,EAAE6K,gBAAgB,EAAErK,UAAU,CAAC,CAAC;IAC3E,OAAOtD,WAAC,CAACe,SAAS,CAAC4M,gBAAgB,CAAC;EACtC,CAAC;EAED,IAAI5H,cAA4B;EAChC,IAAI6H,eAA6B;EACjC,MAAMC,oBAA8B,GAAG,EAAE;EAIzC,KAAK,MAAM7L,OAAO,IAAIX,IAAI,EAAE;IAC1B,IAAI,CAAC4I,6BAA6B,CAACjI,OAAO,CAAC,EAAE;MAC3C;IACF;IAEA,MAAM8L,WAAW,GAAG9L,OAAO,CAACtC,IAAI;IAEhC,IAAI,CAACoO,WAAW,CAACC,MAAM,IAAI/N,WAAC,CAAC4L,aAAa,CAACkC,WAAW,CAACrM,GAAG,CAAC,EAAE;MAC3DoM,oBAAoB,CAACvJ,IAAI,CAACwJ,WAAW,CAACrM,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;IACpD;IAEA,IAAIqO,WAAW,CAACF,WAAW,CAAC,EAAE;MAC5B,QAAQA,WAAW,CAACtN,IAAI;QACtB,KAAK,eAAe;UAElBwM,eAAe,CAACiB,aAAa,CAC3BjM,OAAO,EACP6I,KACF,CAAC;UACD;QACF,KAAK,sBAAsB;UAEzBmC,eAAe,CAACkB,oBAAoB,CAClClM,OAAO,EACP6I,KACF,CAAC;UACD;QACF,KAAK,uBAAuB;UAE1BmC,eAAe,CAACmB,qBAAqB,CACnCnM,OAAO,EACP6I,KACF,CAAC;UACD,IAAIxI,OAAO,KAAK,SAAS,EAAE;YACzB;UACF;QAEF;UACE,IAAIyL,WAAW,CAACC,MAAM,EAAE;YAAA,IAAAK,gBAAA;YACtB,CAAAA,gBAAA,GAAAR,eAAe,YAAAQ,gBAAA,GAAfR,eAAe,GAAK5M,wBAAwB,CAC1C0D,WAAW,EACX,YACF,CAAC;UACH,CAAC,MAAM;YAAA,IAAA2J,eAAA;YACL,CAAAA,eAAA,GAAAtI,cAAc,YAAAsI,eAAA,GAAdtI,cAAc,GAAK/E,wBAAwB,CACzC0D,WAAW,EACX,WACF,CAAC;UACH;UACA;MACJ;MACAyI,oBAAoB,GAAG,IAAI;MAC3BE,oBAAoB,KAApBA,oBAAoB,GAAKS,WAAW,CAAC1F,UAAU,CAACI,IAAI,CAClD4C,+BACF,CAAC;IACH,CAAC,MAAM,IAAI0C,WAAW,CAACtN,IAAI,KAAK,uBAAuB,EAAE;MAEvDwM,eAAe,CAACmB,qBAAqB,CACnCnM,OAAO,EACP6I,KACF,CAAC;MACD,MAAM;QAAEpJ,GAAG;QAAEC,KAAK;QAAEqM,MAAM,EAAEpM,QAAQ;QAAEoK;MAAS,CAAC,GAAG+B,WAAW;MAE9D,MAAMQ,KAAK,GAAGhB,uBAAuB,CAAC,CAAC;MACvC,MAAMiB,QAAQ,GAAG/M,qBAAqB,CAAC8M,KAAK,EAAE5M,KAAK,EAAEC,QAAQ,CAAC;MAC9D,MAAM6M,OAAO,GAAGxM,OAAO,CAACnB,GAAG,CAAC,KAAK,CAAC;MAClC,MAAM,CAACS,OAAO,CAAC,GAAGU,OAAO,CAAClB,WAAW,CAACyN,QAAQ,CAAC;MAE/C,IAAItM,SAAS,EAAEC,SAAS;MACxB,IAAI6J,QAAQ,IAAI,CAACyC,OAAO,CAAC3K,oBAAoB,CAAC,CAAC,EAAE;QAC/C5B,SAAS,GAAG,IAAA2C,wBAAkB,EAC5BmG,uBAAuB,CAACF,KAAK,EAAEpJ,GAAmB,CAAC,EACnDiD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAE;QACF3C,SAAS,GAAGlC,WAAC,CAACe,SAAS,CAACkB,SAAS,CAACgC,IAAoB,CAAC;MACzD,CAAC,MAAM;QACLhC,SAAS,GAAGjC,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC;QAC5BS,SAAS,GAAGlC,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC;MAC9B;MAEAM,oBAAoB,CAClBvC,IAAI,CAACE,IAAI,CAACd,EAAE,EACZ0C,OAAO,EACPW,SAAS,EACTC,SAAS,EACToM,KAAK,EACLvC,QAAQ,EACRpK,QAAQ,EACRU,OACF,CAAC;IACH;IAEA,IAAI,UAAU,IAAIL,OAAO,CAACtC,IAAI,IAAIsC,OAAO,CAACtC,IAAI,CAACqM,QAAQ,EAAE;MACvDqB,0BAA0B,KAA1BA,0BAA0B,GAAK,CAAC1I,WAAW,CAAC/C,QAAQ,CAACK,OAAO,CAACtC,IAAI,CAAC+B,GAAG,CAAC;IACxE;EACF;EAEA,IAAI,CAACyL,eAAe,IAAI,CAACC,oBAAoB,EAAE;IAE7C;EACF;EAEA,MAAMsB,oBAAqC,GAAG,EAAE;EAEhD,IAAIhJ,eAAoD;EACxD,MAAMiH,uBAAuB,GAAG,IAAIrN,GAAG,CAAS,CAAC;EAEjD,IAAIqP,cAA4B,EAAEC,YAA0B;EAC5D,IAAIC,mBAAwC,GAAG,IAAI;EAUnD,SAASC,0BAA0BA,CACjClL,WAA2B,EACO;IAClC,IAAImL,cAAc,GAAG,KAAK;IAC1B,IAAIC,aAAa,GAAG,KAAK;IACzB,MAAM1G,cAAuC,GAAG,EAAE;IAClD,KAAK,MAAM/E,UAAU,IAAIK,WAAW,EAAE;MACpC,IAAIqL,MAAM;MACV,KACG3M,OAAO,KAAK,SAAS,IACcA,OAAO,KAAK,SAAS,KACzDrC,WAAC,CAACiP,kBAAkB,CAAC3L,UAAU,CAAC,EAChC;QACA,IAAItD,WAAC,CAACuG,OAAO,CAACjD,UAAU,CAAC0L,MAAM,CAAC,EAAE;UAChCA,MAAM,GAAGhP,WAAC,CAACuC,cAAc,CAAC,CAAC;QAC7B,CAAC,MAAM,IAAImC,WAAW,CAAC/C,QAAQ,CAAC2B,UAAU,CAAC0L,MAAM,CAAC,EAAE;UAClDA,MAAM,GAAGhP,WAAC,CAACe,SAAS,CAACuC,UAAU,CAAC0L,MAAM,CAAC;QACzC,CAAC,MAAM;UAAA,IAAAE,oBAAA;UACL,CAAAA,oBAAA,GAAAN,mBAAmB,YAAAM,oBAAA,GAAnBN,mBAAmB,GAAK5N,wBAAwB,CAAC0D,WAAW,EAAE,KAAK,CAAC;UACpEsK,MAAM,GAAGhP,WAAC,CAAC8C,oBAAoB,CAC7B,GAAG,EACH9C,WAAC,CAACe,SAAS,CAAC6N,mBAAmB,CAAC,EAChCtL,UAAU,CAAC0L,MACb,CAAC;UACD1L,UAAU,CAAC0L,MAAM,GAAGhP,WAAC,CAACe,SAAS,CAAC6N,mBAAmB,CAAC;QACtD;MACF;MACAvG,cAAc,CAAC/D,IAAI,CAAC0K,MAAM,CAAC;MAC3BF,cAAc,KAAdA,cAAc,GAAK,CAACpK,WAAW,CAAC/C,QAAQ,CAAC2B,UAAU,CAAC;MACpDyL,aAAa,KAAbA,aAAa,GAAK3D,+BAA+B,CAAC9H,UAAU,CAAC;IAC/D;IACA,OAAO;MAAEwL,cAAc;MAAEC,aAAa;MAAE1G;IAAe,CAAC;EAC1D;EAEA,MAAM8G,uBAAuB,GAC3B/B,0BAA0B,IAGtBC,oBAAoB,IAAIhL,OAAO,KAAK,SAAU;EAEpD,IAAI+M,8BAA8B,GAAG,KAAK;EAC1C,IAAIC,oBAAoB,GAAG,CAAC;EAC5B,IAAIC,gBAAgC,GAAG,EAAE;EACzC,IAAIC,kBAAgC;EACpC,IAAIC,sBAAgD,GAAG,EAAE;EACzD,IAAItC,eAAe,EAAE;IACnBwB,cAAc,GAAG1N,wBAAwB,CAAC0D,WAAW,EAAE,WAAW,CAAC;IACnE0K,8BAA8B,GAAG5P,IAAI,CAACiQ,kBAAkB,CAAC,CAAC;IAC1D,CAAC;MAAE7Q,EAAE,EAAE+P,YAAY;MAAEnP;IAAK,CAAC,GAAGa,mBAAmB,CAACb,IAAI,EAAEc,SAAS,CAAC;IAElEd,IAAI,CAACE,IAAI,CAAC0I,UAAU,GAAG,IAAI;IAE3B,MAAMsH,oBAAoB,GAAGxC,eAAe,CAACzG,GAAG,CAACyB,EAAE,IAAIA,EAAE,CAAC5E,UAAU,CAAC;IACrE,MAAMqM,uBAAuB,GAAGD,oBAAoB,CAAClH,IAAI,CAACmD,gBAAgB,CAAC;IAC3E,MAAM;MAAEmD,cAAc;MAAEzG;IAAe,CAAC,GACtCwG,0BAA0B,CAACa,oBAAoB,CAAC;IAElD,MAAM;MAAE7G,QAAQ;MAAEH;IAAK,CAAC,GAAGP,sBAAsB,CAC/CuH,oBAAoB,EACpBrH,cAAc,EACdhG,OACF,CAAC;IACDgN,oBAAoB,GAAGxG,QAAQ,GAAG,CAAC,GAAG,CAAC;IACvCyG,gBAAgB,GAAG5G,IAAI;IAEvB,IACGoG,cAAc,IAAIK,uBAAuB,IAC1CQ,uBAAuB,EACvB;MACAJ,kBAAkB,GAAG/B,iBAAiB,CACpCxN,WAAC,CAACgJ,eAAe,CAACsG,gBAAgB,CAAC,EACnC,WAAW,EACX/B,gBACF,CAAC;IACH;IAEA,IAAI,CAACJ,oBAAoB,EAAE;MAGzB,KAAK,MAAMnL,OAAO,IAAIxC,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC,EAAE;QAC3C,MAAM;UAAEnB;QAAK,CAAC,GAAGsC,OAAO;QACxB,MAAMI,UAAU,GAAG,UAAU,IAAI1C,IAAI,IAAIA,IAAI,CAACqM,QAAQ;QACtD,IAAI3J,UAAU,EAAE;UACd,IAAIJ,OAAO,CAAC4N,eAAe,CAAC;YAAE7B,MAAM,EAAE;UAAK,CAAC,CAAC,EAAE;YAC7C,IAAI,CAAC/L,OAAO,CAACnB,GAAG,CAAC,KAAK,CAAC,CAACgD,oBAAoB,CAAC,CAAC,EAAE;cAC9C,MAAMpC,GAAG,GAAI/B,IAAI,CAAqB+B,GAAG;cACzC,MAAMkD,eAAe,GAAG,IAAAC,wBAAkB,EACxCnD,GAAG,EACHiD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;cACD,IAAIF,eAAe,IAAI,IAAI,EAAE;gBAI3BjF,IAAI,CAAC+B,GAAG,GAAGzB,WAAC,CAACe,SAAS,CAAC4D,eAAe,CAACV,IAAI,CAAC;gBAC5CuL,sBAAsB,CAAClL,IAAI,CAACK,eAAe,CAAC;cAC9C;YACF;UACF,CAAC,MAAM,IAAI6K,sBAAsB,CAAC1Q,MAAM,GAAG,CAAC,EAAE;YAC5CsF,+BAA+B,CAC7BoL,sBAAsB,EACtBxN,OACF,CAAC;YACDwN,sBAAsB,GAAG,EAAE;UAC7B;QACF;MACF;IACF;EACF,CAAC,MAAM;IACL,IAAI,CAAChQ,IAAI,CAACE,IAAI,CAACd,EAAE,EAAE;MACjBY,IAAI,CAACE,IAAI,CAACd,EAAE,GAAGY,IAAI,CAACe,KAAK,CAACsP,qBAAqB,CAAC,OAAO,CAAC;IAC1D;IACAlB,YAAY,GAAG3O,WAAC,CAACe,SAAS,CAACvB,IAAI,CAACE,IAAI,CAACd,EAAE,CAAC;EAC1C;EAEA,IAAIkR,uBAAsC;EAC1C,IAAIC,8BAA8B,GAAG,KAAK;EAE1C,IAAIC,2BAA2B,GAAG,EAAE;EACpC,IAAIC,iCAAiD,GAAG,EAAE;EAE1D,IAAI9C,oBAAoB,EAAE;IACxB,IAAIpH,cAAc,EAAE;MAClB,MAAMJ,aAAa,GAAG3F,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACe,SAAS,CAACgF,cAAc,CAAC,EAAE,CAClE/F,WAAC,CAACuC,cAAc,CAAC,CAAC,CACnB,CAAC;MACFyN,2BAA2B,CAAC1L,IAAI,CAACqB,aAAa,CAAC;IACjD;IACA,KAAK,MAAM3D,OAAO,IAAIX,IAAI,EAAE;MAC1B,IAAI,CAAC4I,6BAA6B,CAACjI,OAAO,CAAC,EAAE;QAC3C,IACEiO,iCAAiC,CAACnR,MAAM,GAAG,CAAC,IAC5CkD,OAAO,CAACkO,aAAa,CAAC,CAAC,EACvB;UACA7K,+BAA+B,CAC7B4K,iCAAiC,EACjCjO,OACF,CAAC;UACDiO,iCAAiC,GAAG,EAAE;QACxC;QACA;MACF;MAEA,MAAM;QAAEvQ;MAAK,CAAC,GAAGsC,OAAO;MACxB,MAAMoG,UAAU,GAAG1I,IAAI,CAAC0I,UAAU;MAElC,MAAM+H,aAAa,GAAG,CAAC,EAAC/H,UAAU,YAAVA,UAAU,CAAEtJ,MAAM;MAE1C,MAAMsD,UAAU,GAAG,UAAU,IAAI1C,IAAI,IAAIA,IAAI,CAACqM,QAAQ;MAEtD,IAAIpM,IAAI,GAAG,aAAa;MAExB,IAAID,IAAI,CAAC+B,GAAG,CAACjB,IAAI,KAAK,aAAa,EAAE;QACnCb,IAAI,GAAGD,IAAI,CAAC+B,GAAG,CAAC7C,EAAE,CAACe,IAAI;MACzB,CAAC,MAAM,IAAI,CAACyC,UAAU,IAAI1C,IAAI,CAAC+B,GAAG,CAACjB,IAAI,KAAK,YAAY,EAAE;QACxDb,IAAI,GAAGD,IAAI,CAAC+B,GAAG,CAAC9B,IAAI;MACtB;MACA,IAAIwJ,eAAgE;MACpE,IAAID,kBAAkB;MAEtB,IAAIiH,aAAa,EAAE;QACjB,MAAMT,oBAAoB,GAAGtH,UAAU,CAAC3B,GAAG,CAAC2J,CAAC,IAAIA,CAAC,CAAC9M,UAAU,CAAC;QAC9D,MAAM;UAAEwL,cAAc;UAAEC,aAAa;UAAE1G;QAAe,CAAC,GACrDwG,0BAA0B,CAACa,oBAAoB,CAAC;QAClD,MAAM;UAAEhH,IAAI;UAAEG;QAAS,CAAC,GAAGV,sBAAsB,CAC/CuH,oBAAoB,EACpBrH,cAAc,EACdhG,OACF,CAAC;QACD6G,kBAAkB,GAAGL,QAAQ;QAC7BM,eAAe,GAAGT,IAAI,CAAC5J,MAAM,KAAK,CAAC,GAAG4J,IAAI,CAAC,CAAC,CAAC,GAAG1I,WAAC,CAACgJ,eAAe,CAACN,IAAI,CAAC;QACvE,IAAIqG,aAAa,IAAKD,cAAc,IAAIK,uBAAwB,EAAE;UAChEhG,eAAe,GAAGqE,iBAAiB,CACjCrE,eAAe,EACfxJ,IAAI,GAAG,MAAM,EACb6P,sBACF,CAAC;QACH;MACF;MAEA,IAAIpN,UAAU,EAAE;QACd,IAAI,CAACJ,OAAO,CAACnB,GAAG,CAAC,KAAK,CAAC,CAACgD,oBAAoB,CAAC,CAAC,EAAE;UAC9C,MAAMpC,GAAG,GAAG/B,IAAI,CAAC+B,GAAmB;UACpC,MAAMkD,eAAe,GAAG,IAAAC,wBAAkB,EACxCuL,aAAa,GAAGpF,uBAAuB,CAACF,KAAK,EAAEpJ,GAAG,CAAC,GAAGA,GAAG,EACzDiD,WAAW,EACXA,WAAW,CAACG,WAAW,CAAC,aAAa,CACvC,CAAC;UACD,IAAIF,eAAe,IAAI,IAAI,EAAE;YAI3B,IAAIuI,eAAe,IAAIlL,OAAO,CAAC4N,eAAe,CAAC;cAAE7B,MAAM,EAAE;YAAK,CAAC,CAAC,EAAE;cAChErO,IAAI,CAAC+B,GAAG,GAAGzB,WAAC,CAACe,SAAS,CAAC4D,eAAe,CAACV,IAAI,CAAC;cAC5CuL,sBAAsB,CAAClL,IAAI,CAACK,eAAe,CAAC;YAC9C,CAAC,MAAM;cACLjF,IAAI,CAAC+B,GAAG,GAAGkD,eAAe;YAC5B;UACF;QACF;MACF;MAEA,MAAM;QAAElD,GAAG;QAAEsM,MAAM,EAAEpM;MAAS,CAAC,GAAGjC,IAAI;MAEtC,MAAM2Q,SAAS,GAAG5O,GAAG,CAACjB,IAAI,KAAK,aAAa;MAE5C,MAAMsG,IAAI,GAAGgB,cAAc,CAAC9F,OAAO,CAAC;MAEpC,IAAIqO,SAAS,IAAI,CAAC1O,QAAQ,EAAE;QAC1B,IAAIwO,aAAa,EAAE;UACjBJ,8BAA8B,GAAG,IAAI;QACvC;QACA,IAAI/P,WAAC,CAACsQ,sBAAsB,CAAC5Q,IAAI,CAAC,IAAI,CAACoQ,uBAAuB,EAAE;UAC9DA,uBAAuB,GAAGrO,GAAG;QAC/B;MACF;MAEA,IAAIO,OAAO,CAACuO,aAAa,CAAC;QAAEzJ,IAAI,EAAE;MAAc,CAAC,CAAC,EAAE;QAClDrB,eAAe,GAAGzD,OAAO;MAC3B;MAEA,IAAIuH,MAAsB;MAC1B,IAAI4G,aAAa,EAAE;QACjB,IAAI/G,cAEH;QAED,IAAIoH,QAAsB;QAE1B,IAAIpO,UAAU,EAAE;UACdoO,QAAQ,GAAG5M,sBAAsB,CAC/B5B,OAAO,CAACnB,GAAG,CAAC,KAAK,CACnB,CAAC;QACH,CAAC,MAAM,IAAIY,GAAG,CAACjB,IAAI,KAAK,aAAa,EAAE;UACrCgQ,QAAQ,GAAGxQ,WAAC,CAACgM,aAAa,CAACvK,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;QACzC,CAAC,MAAM,IAAI8B,GAAG,CAACjB,IAAI,KAAK,YAAY,EAAE;UACpCgQ,QAAQ,GAAGxQ,WAAC,CAACgM,aAAa,CAACvK,GAAG,CAAC9B,IAAI,CAAC;QACtC,CAAC,MAAM;UACL6Q,QAAQ,GAAGxQ,WAAC,CAACe,SAAS,CAACU,GAAmB,CAAC;QAC7C;QAEA,IAAIqF,IAAI,KAAKS,QAAQ,EAAE;UACrB,MAAM;YAAE7F;UAAM,CAAC,GAAGM,OAAO,CAACtC,IAA+B;UAEzD,MAAMqK,MAAsB,GACO1H,OAAO,KAAK,SAAS,IAAKV,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACuC,cAAc,CAAC,CAAC,CAAC;UAE1B,IAAIb,KAAK,EAAE;YACTqI,MAAM,CAACzF,IAAI,CAACtE,WAAC,CAACe,SAAS,CAACW,KAAK,CAAC,CAAC;UACjC;UAEA,MAAM4M,KAAK,GAAGhB,uBAAuB,CAAC,CAAC;UACvC,MAAMmD,cAAc,GAAGzP,wBAAwB,CAC7C0D,WAAW,EACV,QAAO/E,IAAK,EACf,CAAC;UACD,MAAM+Q,QAAQ,GAAG1Q,WAAC,CAACiG,cAAc,CAC/BjG,WAAC,CAACe,SAAS,CAAC0P,cAAc,CAAC,EAC3B1G,MACF,CAAC;UAED,MAAMwE,QAAQ,GAAG/M,qBAAqB,CAAC8M,KAAK,EAAEoC,QAAQ,EAAE/O,QAAQ,CAAC;UACjE,MAAM,CAACL,OAAO,CAAC,GAAGU,OAAO,CAAClB,WAAW,CAACyN,QAAQ,CAAC;UAE/C,IAAI8B,SAAS,EAAE;YACbjH,cAAc,GAAGhG,wBAAwB,CAACkL,KAAK,EAAEjM,OAAO,CAAC;YAEzD,MAAMsH,KAAK,GAAG3I,wBAAwB,CAAC0D,WAAW,EAAG,OAAM/E,IAAK,EAAC,CAAC;YAClE,MAAMiK,KAAK,GAAG5I,wBAAwB,CAAC0D,WAAW,EAAG,OAAM/E,IAAK,EAAC,CAAC;YAElE+J,mBAAmB,CAACrH,OAAO,EAAEf,OAAO,EAAEG,GAAG,EAAEkI,KAAK,EAAEC,KAAK,EAAEjI,QAAQ,CAAC;YAElE4H,MAAM,GAAG,CAACkH,cAAc,EAAE9G,KAAK,EAAEC,KAAK,CAAC;UACzC,CAAC,MAAM;YACL7H,oBAAoB,CAClBvC,IAAI,CAACE,IAAI,CAACd,EAAE,EACZ0C,OAAO,EACPtB,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChBzB,WAAC,CAACgE,sBAAsB,CAACvC,GAAG,CAAC,GACzBzB,WAAC,CAACe,SAAS,CAACU,GAAG,CAACwC,IAAoB,CAAC,GACrCjE,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EACpB6M,KAAK,EACLlM,UAAU,EACVT,QAAQ,EACRU,OACF,CAAC;YACDkH,MAAM,GAAG,CAACkH,cAAc,CAAC;UAC3B;QACF,CAAC,MAAM,IAAI3J,IAAI,KAAKQ,KAAK,EAAE;UACzB,MAAMqJ,MAAM,GAAG3P,wBAAwB,CAAC0D,WAAW,EAAG,QAAO/E,IAAK,EAAC,CAAC;UACpE,MAAMiR,SAAS,GACb5O,OAAO,CACPnB,GAAG,CAAC,OAAO,CAAC;UAEd,MAAMgQ,IAAoB,GACSxO,OAAO,KAAK,SAAS,IAAKV,QAAQ,GAC/D,EAAE,GACF,CAAC3B,WAAC,CAACuC,cAAc,CAAC,CAAC,CAAC;UAC1B,IAAIqO,SAAS,CAAClR,IAAI,EAAEmR,IAAI,CAACvM,IAAI,CAACsM,SAAS,CAAClR,IAAI,CAAC;UAE7CkR,SAAS,CAAC9P,WAAW,CAACd,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACe,SAAS,CAAC4P,MAAM,CAAC,EAAEE,IAAI,CAAC,CAAC;UAElEtH,MAAM,GAAG,CAACoH,MAAM,CAAC;UAEjB,IAAIN,SAAS,EAAE;YACbjH,cAAc,GAAGhG,wBAAwB,CAAC3B,GAAG,EAAEY,OAAO,CAAC;UACzD;QACF,CAAC,MAAM,IAAIgO,SAAS,EAAE;UACpB,MAAMS,MAAM,GAAG9P,wBAAwB,CAAC0D,WAAW,EAAG,QAAO/E,IAAK,EAAC,CAAC;UACpE4J,MAAM,GAAG,CAACuH,MAAM,CAAC;UAEjB,MAAMC,aAAa,GAAG,IAAIC,4BAAa,CAAC;YACtClE,aAAa;YACbmE,UAAU,EAAEjP,OAAyC;YACrDkP,SAAS,EAAEvC,YAAY;YACvBwC,QAAQ,EAAE3R,IAAI,CAACE,IAAI,CAAC0B,UAAU;YAC9BgQ,IAAI,EAAEvG,KAAK,CAACuG,IAAI;YAChBC,aAAa,EAAE1C;UACjB,CAAC,CAAC;UAEFoC,aAAa,CAACO,OAAO,CAAC,CAAC;UAEvBlI,cAAc,GAAG,CACfoB,yCAAyC,CACvCxI,OAAO,CAACtC,IACV,CAAC,CACF;UAED,IAAIoH,IAAI,KAAKW,MAAM,IAAIX,IAAI,KAAKY,MAAM,EAAE;YACtCmC,mBAAmB,CACjB7H,OAAO,EACPhC,WAAC,CAACe,SAAS,CAACU,GAAG,CAAC,EAChBzB,WAAC,CAACe,SAAS,CAAC+P,MAAM,CAAC,EACnBnP,QACF,CAAC;UACH,CAAC,MAAM;YACL,MAAMjC,IAAI,GAAGsC,OAAO,CAACtC,IAA4B;YAGjDF,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACzBiB,WAAC,CAAC4B,oBAAoB,CAACH,GAAG,EAAEzB,WAAC,CAACe,SAAS,CAAC+P,MAAM,CAAC,EAAE,EAAE,EAAEpR,IAAI,CAACqO,MAAM,CAClE,CAAC;YAEDrB,uBAAuB,CAACjN,GAAG,CAACgC,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;YAExCqC,OAAO,CAACuP,MAAM,CAAC,CAAC;UAClB;QACF;QAEA9C,oBAAoB,CAACnK,IAAI,CAAC;UACxBwC,IAAI;UACJqC,eAAe;UACfD,kBAAkB;UAClBvJ,IAAI,EAAE6Q,QAAQ;UACd7O,QAAQ;UACRyH,cAAc;UACdG;QACF,CAAC,CAAC;QAEF,IAAIvH,OAAO,CAACtC,IAAI,EAAE;UAChBsC,OAAO,CAACtC,IAAI,CAAC0I,UAAU,GAAG,IAAI;QAChC;MACF;MAEA,IAAIhG,UAAU,IAAIoN,sBAAsB,CAAC1Q,MAAM,GAAG,CAAC,EAAE;QACnD,IAAIoO,eAAe,IAAIlL,OAAO,CAAC4N,eAAe,CAAC;UAAE7B,MAAM,EAAE;QAAK,CAAC,CAAC,EAAE,CAMlE,CAAC,MAAM;UACL3J,+BAA+B,CAC7BoL,sBAAsB,EACrB1I,IAAI,KAAKS,QAAQ,GACdvF,OAAO,CAACwP,cAAc,CAAC,CAAC,GACxBxP,OACN,CAAC;UACDwN,sBAAsB,GAAG,EAAE;QAC7B;MACF;MAEA,IACEQ,2BAA2B,CAAClR,MAAM,GAAG,CAAC,IACtC,CAAC6C,QAAQ,KACRmF,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,CAAC,EACrC;QACArC,oCAAoC,CAClC8K,2BAA2B,EAC3BhO,OACF,CAAC;QACDgO,2BAA2B,GAAG,EAAE;MAClC;MAEA,IACEC,iCAAiC,CAACnR,MAAM,GAAG,CAAC,IAC5C6C,QAAQ,KACPmF,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,CAAC,EACrC;QACArC,oCAAoC,CAClC+K,iCAAiC,EACjCjO,OACF,CAAC;QACDiO,iCAAiC,GAAG,EAAE;MACxC;MAEA,IAAIE,aAAa,IAAI9N,OAAO,KAAK,SAAS,EAAE;QAC1C,IAAIyE,IAAI,KAAKQ,KAAK,IAAIR,IAAI,KAAKS,QAAQ,EAAE;UACvC,MAAMkK,WAAW,GAAGzQ,wBAAwB,CAC1C0D,WAAW,EACV,cAAa/E,IAAK,EACrB,CAAC;UACD4J,MAAM,CAACjF,IAAI,CAACmN,WAAW,CAAC;UACxB,MAAMC,aAAa,GAAG1R,WAAC,CAACiG,cAAc,CACpCjG,WAAC,CAACe,SAAS,CAAC0Q,WAAW,CAAC,EACxB9P,QAAQ,GAAG,EAAE,GAAG,CAAC3B,WAAC,CAACuC,cAAc,CAAC,CAAC,CACrC,CAAC;UACD,IAAI,CAACZ,QAAQ,EAAE;YACbqO,2BAA2B,CAAC1L,IAAI,CAACoN,aAAa,CAAC;UACjD,CAAC,MAAM;YACLzB,iCAAiC,CAAC3L,IAAI,CAACoN,aAAa,CAAC;UACvD;QACF;MACF;IACF;EACF;EAEA,IAAIlC,sBAAsB,CAAC1Q,MAAM,GAAG,CAAC,EAAE;IACrC,MAAM6S,QAAQ,GAAGnS,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC;IACtC,IAAI+Q,mBAA8D;IAClE,KAAK,IAAIjJ,CAAC,GAAGgJ,QAAQ,CAAC7S,MAAM,GAAG,CAAC,EAAE6J,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC7C,MAAMnJ,IAAI,GAAGmS,QAAQ,CAAChJ,CAAC,CAAC;MACxB,MAAMjJ,IAAI,GAAGF,IAAI,CAACE,IAAuC;MACzD,IAAIA,IAAI,CAACqM,QAAQ,EAAE;QACjB,IAAImB,eAAe,IAAIlN,WAAC,CAAC4P,eAAe,CAAClQ,IAAI,EAAE;UAAEqO,MAAM,EAAE;QAAK,CAAC,CAAC,EAAE;UAChE;QACF;QACA6D,mBAAmB,GAAGpS,IAAiD;QACvE;MACF;IACF;IACA,IAAIoS,mBAAmB,IAAI,IAAI,EAAE;MAC/BpN,8BAA8B,CAC5BgL,sBAAsB,EACtBoC,mBACF,CAAC;MACDpC,sBAAsB,GAAG,EAAE;IAC7B,CAAC,MAAM,CAIP;EACF;EAEA,IAAIQ,2BAA2B,CAAClR,MAAM,GAAG,CAAC,EAAE;IAC1C,MAAMkI,cAAc,GAAG,CAAC,CAACxH,IAAI,CAACE,IAAI,CAAC0B,UAAU;IAC7C,IAAIqE,eAAe,EAAE;MACnB,IAAIuB,cAAc,EAAE;QAClBZ,0CAA0C,CACxC4J,2BAA2B,EAC3BvK,eAAe,EACfM,cACF,CAAC;MACH,CAAC,MAAM;QACLP,+BAA+B,CAC7BwK,2BAA2B,EAC3BvK,eACF,CAAC;MACH;IACF,CAAC,MAAM;MACLjG,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACtC,OAAO,CACzBgI,gCAAgC,CAC9BiJ,2BAA2B,EAC3BhJ,cACF,CACF,CAAC;IACH;IACAgJ,2BAA2B,GAAG,EAAE;EAClC;EAEA,IAAIC,iCAAiC,CAACnR,MAAM,GAAG,CAAC,EAAE;IAChDU,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACiD,IAAI,CACtB8C,gCAAgC,CAAC6I,iCAAiC,CACpE,CAAC;IACDA,iCAAiC,GAAG,EAAE;EACxC;EAEA,MAAM4B,0BAA0B,GAC9B9J,qBAAqB,CAAC0G,oBAAoB,CAAC;EAE7C,MAAMqD,kBAAkB,GAAGhJ,uBAAuB,CAChBzG,OAAO,KAAK,SAAS,GACjDoM,oBAAoB,GACpBoD,0BAA0B,EAC9BxP,OACF,CAAC;EAED,MAAM0P,aAA6B,GAAG1I,8BAA8B,CAClEwI,0BACF,CAAC;EAED,IAAI9L,cAAc,EAAE;IAClBgM,aAAa,CAACzN,IAAI,CAACyB,cAAc,CAAC;EACpC;EAEA,IAAI6H,eAAe,EAAE;IACnBmE,aAAa,CAACzN,IAAI,CAACsJ,eAAe,CAAC;EACrC;EAEA,MAAMoE,WAA2B,GAAG,EAAE;EACtC,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,MAAMC,aAAa,GACjBxD,cAAc,IAAI1O,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACe,SAAS,CAAC2N,cAAc,CAAC,EAAE,EAAE,CAAC;EAErE,IAAIyD,iBAAiB,GAAG3S,IAAI;EAC5B,MAAM4S,aAAa,GAAG5S,IAAI,CAACE,IAAI;EAE/B,MAAM2S,cAAwC,GAAG,EAAE;EACnD,IAAInF,eAAe,EAAE;IACnB8E,WAAW,CAAC1N,IAAI,CAACqK,YAAY,EAAED,cAAc,CAAC;IAC9C,MAAM4D,OAIH,GAAG,EAAE;IACR9S,IAAI,CAACqB,GAAG,CAAC,WAAW,CAAC,CAAC0R,OAAO,CAACvQ,OAAO,IAAI;MAGvC,IAAIA,OAAO,CAACkO,aAAa,CAAC,CAAC,EAAE;QAC3B,IAAIjE,wBAAwB,CAACjK,OAAO,EAAE6L,oBAAoB,CAAC,EAAE;UAC3D,MAAM2E,oBAAoB,GAAGhF,iBAAiB,CAC5CpD,4BAA4B,CAACpI,OAAO,CAACtC,IAAI,CAAC,EAC1C,aAAa,EACb2S,cACF,CAAC;UACDpC,iCAAiC,CAAC3L,IAAI,CACpCtE,WAAC,CAACiG,cAAc,CACdjG,WAAC,CAAC2C,gBAAgB,CAAC6P,oBAAoB,EAAExS,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,EAC9D,CAACF,WAAC,CAACuC,cAAc,CAAC,CAAC,CACrB,CACF,CAAC;QACH,CAAC,MAAM;UACL0N,iCAAiC,CAAC3L,IAAI,CACpC4F,iBAAiB,CAAClI,OAAO,CAACtC,IAAI,CAChC,CAAC;QACH;QACAsC,OAAO,CAACuP,MAAM,CAAC,CAAC;QAChB;MACF;MAEA,IACE,CAACvP,OAAO,CAAC4N,eAAe,CAAC,CAAC,IAAI5N,OAAO,CAACsO,sBAAsB,CAAC,CAAC,KAC9DtO,OAAO,CAACtC,IAAI,CAACqO,MAAM,EACnB;QACA,MAAM6C,SAAS,GACb5O,OAAO,CACPnB,GAAG,CAAC,OAAO,CAAC;QACd,IAAIoL,wBAAwB,CAAC2E,SAAS,EAAE/C,oBAAoB,CAAC,EAAE;UAC7D,MAAM4E,mBAAmB,GAAGjF,iBAAiB,CAC3ClD,yBAAyB,CAACsG,SAAS,CAAClR,IAAI,CAAC,EACzC,YAAY,EACZ2S,cACF,CAAC;UACDzB,SAAS,CAAC9P,WAAW,CACnBd,WAAC,CAACiG,cAAc,CACdjG,WAAC,CAAC2C,gBAAgB,CAAC8P,mBAAmB,EAAEzS,WAAC,CAACE,UAAU,CAAC,MAAM,CAAC,CAAC,EAC7D,CAACF,WAAC,CAACuC,cAAc,CAAC,CAAC,CACrB,CACF,CAAC;QACH;QACA,IAAI0N,iCAAiC,CAACnR,MAAM,GAAG,CAAC,EAAE;UAChDoG,oCAAoC,CAClC+K,iCAAiC,EACjCjO,OACF,CAAC;UACDiO,iCAAiC,GAAG,EAAE;QACxC;QACAjO,OAAO,CAACtC,IAAI,CAACqO,MAAM,GAAG,KAAK;QAC3BuE,OAAO,CAAChO,IAAI,CAACtC,OAAO,CAACtC,IAAI,CAAC;QAC1BsC,OAAO,CAACuP,MAAM,CAAC,CAAC;MAClB,CAAC,MAAM,IAAIvP,OAAO,CAAC0Q,oBAAoB,CAAC;QAAE3E,MAAM,EAAE;MAAK,CAAC,CAAC,EAAE;QAGzD,IAAI9B,wBAAwB,CAACjK,OAAO,EAAE6L,oBAAoB,CAAC,EAAE;UAC3D,MAAMkD,aAAa,GAAG,IAAIC,4BAAa,CAAC;YACtClE,aAAa;YACbmE,UAAU,EAAEjP,OAAO;YACnBkP,SAAS,EAAEvC,YAAY;YACvBwC,QAAQ,EAAE3R,IAAI,CAACE,IAAI,CAAC0B,UAAU;YAC9BgQ,IAAI,EAAEvG,KAAK,CAACuG,IAAI;YAChBC,aAAa,EAAE1C;UACjB,CAAC,CAAC;UAEFoC,aAAa,CAACO,OAAO,CAAC,CAAC;UAEvB,MAAMqB,uBAAuB,GAAGnF,iBAAiB,CAC/ChD,yCAAyC,CAACxI,OAAO,CAACtC,IAAI,CAAC,EACvDsC,OAAO,CAACnB,GAAG,CAAC,QAAQ,CAAC,CAACnB,IAAI,CAACC,IAAI,EAC/B0S,cACF,CAAC;UAED,IAAItF,oBAAoB,EAAE;YACxB/K,OAAO,CAACtC,IAAI,CAACqK,MAAM,GAAG,CAAC/J,WAAC,CAACmH,WAAW,CAACnH,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;YAC1D8B,OAAO,CAACtC,IAAI,CAAC2B,IAAI,GAAGrB,WAAC,CAACyC,cAAc,CAAC,CACnCzC,WAAC,CAAC0C,eAAe,CACf1C,WAAC,CAACiG,cAAc,CACdjG,WAAC,CAAC2C,gBAAgB,CAChBgQ,uBAAuB,EACvB3S,WAAC,CAACE,UAAU,CAAC,OAAO,CACtB,CAAC,EACD,CAACF,WAAC,CAACuC,cAAc,CAAC,CAAC,EAAEvC,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAC1C,CACF,CAAC,CACF,CAAC;UACJ,CAAC,MAAM;YACL8B,OAAO,CAACtC,IAAI,CAACqK,MAAM,GAAG/H,OAAO,CAACtC,IAAI,CAACqK,MAAM,CAACtD,GAAG,CAAC,CAACmM,CAAC,EAAEjK,CAAC,KAAK;cACtD,IAAI3I,WAAC,CAAC6S,aAAa,CAACD,CAAC,CAAC,EAAE;gBACtB,OAAO5S,WAAC,CAACmH,WAAW,CAACnH,WAAC,CAACE,UAAU,CAAC,KAAK,CAAC,CAAC;cAC3C,CAAC,MAAM;gBACL,OAAOF,WAAC,CAACE,UAAU,CAAC,GAAG,GAAGyI,CAAC,CAAC;cAC9B;YACF,CAAC,CAAC;YACF3G,OAAO,CAACtC,IAAI,CAAC2B,IAAI,GAAGrB,WAAC,CAACyC,cAAc,CAAC,CACnCzC,WAAC,CAAC0C,eAAe,CACf1C,WAAC,CAACiG,cAAc,CACdjG,WAAC,CAAC2C,gBAAgB,CAChBgQ,uBAAuB,EACvB3S,WAAC,CAACE,UAAU,CAAC,OAAO,CACtB,CAAC,EACD,CAACF,WAAC,CAACuC,cAAc,CAAC,CAAC,EAAEvC,WAAC,CAACE,UAAU,CAAC,WAAW,CAAC,CAChD,CACF,CAAC,CACF,CAAC;UACJ;QACF;QACA8B,OAAO,CAACtC,IAAI,CAACqO,MAAM,GAAG,KAAK;QAC3BuE,OAAO,CAAChO,IAAI,CAACtC,OAAO,CAACtC,IAAI,CAAC;QAC1BsC,OAAO,CAACuP,MAAM,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,IAAIe,OAAO,CAACxT,MAAM,GAAG,CAAC,IAAImR,iCAAiC,CAACnR,MAAM,GAAG,CAAC,EAAE;MACtE,MAAMgU,YAAY,GAAGzP,cAAQ,CAACC,UAAU,CAACC,GAAI;AACnD,wBAAwBsH,KAAK,CAACC,SAAS,CAAC,UAAU,CAAE;AACpD,OAA4B;MACtBgI,YAAY,CAACzR,IAAI,CAACA,IAAI,GAAG,CAOvBrB,WAAC,CAAC8B,aAAa,CACb9B,WAAC,CAAC+S,YAAY,CAACX,aAAa,CAAC,EAC7BvQ,SAAS,EACTA,SAAS,EACTA,SAAS,EACM,IAAI,EACN,IACf,CAAC,EACD,GAAGyQ,OAAO,CACX;MAED,MAAMU,eAA+B,GAAG,EAAE;MAE1C,MAAMC,OAAO,GAAGjT,WAAC,CAACkT,aAAa,CAACJ,YAAY,EAAE,EAAE,CAAC;MAEjD,IAAI7C,iCAAiC,CAACnR,MAAM,GAAG,CAAC,EAAE;QAChDkU,eAAe,CAAC1O,IAAI,CAAC,GAAG2L,iCAAiC,CAAC;MAC5D;MACA,IAAIiC,aAAa,EAAE;QACjBD,iBAAiB,GAAG,IAAI;QACxBe,eAAe,CAAC1O,IAAI,CAAC4N,aAAa,CAAC;MACrC;MACA,IAAIc,eAAe,CAAClU,MAAM,GAAG,CAAC,EAAE;QAC9BkU,eAAe,CAACjU,OAAO,CACrBiB,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACiH,KAAK,CAAC,CAAC,EAAE,CAACjH,WAAC,CAACe,SAAS,CAAC4N,YAAY,CAAC,CAAC,CACzD,CAAC;QAGDmE,YAAY,CAACzR,IAAI,CAACA,IAAI,CAACiD,IAAI,CACzByC,gCAAgC,CAC9BiM,eAAe,EACM,KACvB,CACF,CAAC;MACH,CAAC,MAAM;QACLC,OAAO,CAACE,SAAS,CAAC7O,IAAI,CAACtE,WAAC,CAACe,SAAS,CAAC4N,YAAY,CAAC,CAAC;MACnD;MAEA,MAAM,CAACrN,OAAO,CAAC,GAAG9B,IAAI,CAACsB,WAAW,CAACmS,OAAO,CAAC;MAG3Cd,iBAAiB,GACf7Q,OAAO,CAACT,GAAG,CAAC,QAAQ,CAAC,CAACA,GAAG,CAAC,MAAM,CAAC,CAEhCA,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CACdA,GAAG,CAAC,KAAK,CAAC;IACf;EACF;EACA,IAAI,CAACoR,iBAAiB,IAAIC,aAAa,EAAE;IACvC1S,IAAI,CAACE,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACiD,IAAI,CACtBtE,WAAC,CAACqH,WAAW,CAAC,CAACrH,WAAC,CAAC6C,mBAAmB,CAACqP,aAAa,CAAC,CAAC,CACtD,CAAC;EACH;EAEA,IAAI;IAAE9Q;EAAW,CAAC,GAAGgR,aAAa;EAClC,IACEhR,UAAU,KAERiB,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,GACvB;IACA,MAAMzD,EAAE,GAAGY,IAAI,CAACe,KAAK,CAAC6S,qBAAqB,CAAChS,UAAU,CAAC;IACvD,IAAIxC,EAAE,EAAE;MACNwT,aAAa,CAAChR,UAAU,GAAGpB,WAAC,CAAC8C,oBAAoB,CAAC,GAAG,EAAElE,EAAE,EAAEwC,UAAU,CAAC;MACtEA,UAAU,GAAGxC,EAAE;IACjB;EACF;EAEA,MAAMyU,qBAAqB,GAAGrT,WAAC,CAACqH,WAAW,CAAC,EAAE,CAAC;EAC/C+K,aAAa,CAAC/Q,IAAI,CAACA,IAAI,CAACtC,OAAO,CAACsU,qBAAqB,CAAC;EACtD,MAAMC,aAAa,GAAGD,qBAAqB,CAAChS,IAAI;EAChD,IAAImO,sBAAsB,CAAC1Q,MAAM,GAAG,CAAC,EAAE;IACrC,MAAM6S,QAAQ,GAAGQ,iBAAiB,CAACtR,GAAG,CAAC,WAAW,CAAC;IACnD,IAAI0S,kBAA6D;IACjE,KAAK,MAAM/T,IAAI,IAAImS,QAAQ,EAAE;MAC3B,IACE,CAACnS,IAAI,CAACoQ,eAAe,CAAC,CAAC,IAAIpQ,IAAI,CAAC+Q,aAAa,CAAC,CAAC,KAC9C/Q,IAAI,CAACE,IAAI,CAAmBoH,IAAI,KAAK,aAAa,EACnD;QACAyM,kBAAkB,GAAG/T,IAAI;QACzB;MACF;IACF;IACA,IAAI+T,kBAAkB,IAAI,IAAI,EAAE;MAE9BzH,oBAAoB,CAACyH,kBAAkB,CAAC;MACxCnP,+BAA+B,CAC7BoL,sBAAsB,EACtB+D,kBACF,CAAC;IACH,CAAC,MAAM;MAILnB,aAAa,CAAC/Q,IAAI,CAACA,IAAI,CAACtC,OAAO,CAC7BiB,WAAC,CAAC8B,aAAa,CACb9B,WAAC,CAACuB,kBAAkB,CAAC,CACnB,GAAGiO,sBAAsB,EACzBxP,WAAC,CAACgM,aAAa,CAAC,GAAG,CAAC,CACrB,CAAC,EACFnK,SAAS,EACTA,SAAS,EACTA,SAAS,EACM,IAAI,EACN,IACf,CACF,CAAC;MACDyR,aAAa,CAAChP,IAAI,CAChBtE,WAAC,CAAC6C,mBAAmB,CACnB7C,WAAC,CAACoF,eAAe,CACf,QAAQ,EACRpF,WAAC,CAAC2C,gBAAgB,CAAC3C,WAAC,CAACuC,cAAc,CAAC,CAAC,EAAEvC,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,CAC1D,CACF,CACF,CAAC;IACH;IACAsP,sBAAsB,GAAG,EAAE;EAC7B;EAEA8D,aAAa,CAAChP,IAAI,CAChBtE,WAAC,CAAC6C,mBAAmB,CACnB2Q,sBAAsB,CACpBzB,aAAa,EACbC,WAAW,EACXF,kBAAkB,GAAA7E,mBAAA,GAClBsC,kBAAkB,YAAAtC,mBAAA,GAAIjN,WAAC,CAACgJ,eAAe,CAACsG,gBAAgB,CAAC,EACzDtP,WAAC,CAAC4I,cAAc,CAACyG,oBAAoB,CAAC,EACtCU,8BAA8B,GAAGD,uBAAuB,GAAG,IAAI,EAC/D,OAAOxP,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAGuB,SAAS,EACrD7B,WAAC,CAACe,SAAS,CAACK,UAAU,CAAC,EACvByJ,KAAK,EACLxI,OACF,CACF,CACF,CAAC;EACD,IAAIuL,eAAe,EAAE;IACnB0F,aAAa,CAAChP,IAAI,CAChBtE,WAAC,CAAC6C,mBAAmB,CACnB7C,WAAC,CAACiG,cAAc,CAACjG,WAAC,CAACe,SAAS,CAAC6M,eAAe,CAAC,EAAE,CAAC5N,WAAC,CAACuC,cAAc,CAAC,CAAC,CAAC,CACrE,CACF,CAAC;EACH;EACA,IAAI8P,cAAc,CAACvT,MAAM,GAAG,CAAC,EAAE;IAC7BwU,aAAa,CAAChP,IAAI,CAChB,GAAG+N,cAAc,CAAC5L,GAAG,CAACC,IAAI,IAAI1G,WAAC,CAAC6C,mBAAmB,CAAC6D,IAAI,CAAC,CAC3D,CAAC;EACH;EAIAlH,IAAI,CAACiU,YAAY,CAAClG,gBAAgB,CAAC9G,GAAG,CAACC,IAAI,IAAI1G,WAAC,CAAC6C,mBAAmB,CAAC6D,IAAI,CAAC,CAAC,CAAC;EAE5E,IAAI0I,8BAA8B,EAAE;IAClC5P,IAAI,CAACiU,YAAY,CACfzT,WAAC,CAAC0T,mBAAmB,CAAC,KAAK,EAAE,CAC3B1T,WAAC,CAAC2T,kBAAkB,CAAC3T,WAAC,CAACe,SAAS,CAAC4N,YAAY,CAAC,CAAC,CAChD,CACH,CAAC;EACH;EAEA,IAAIjC,uBAAuB,CAACkH,IAAI,GAAG,CAAC,EAAE;IACpCnH,6BAA6B,CAACjN,IAAI,EAAEkN,uBAAuB,CAAC;EAC9D;EAGAlN,IAAI,CAACe,KAAK,CAACsT,KAAK,CAAC,CAAC;EAElB,OAAOrU,IAAI;AACb;AAEA,SAASgU,sBAAsBA,CAC7BzB,aAA6B,EAC7BC,WAA2B,EAC3BF,kBAAoD,EACpDxC,gBAAkD,EAClDD,oBAAsC,EACtCyE,qBAA2C,EAC3CC,YAAwD,EACxD3S,UAA+B,EAC/ByJ,KAAiB,EACjBxI,OAA6B,EAC7B;EACA,IAAI2R,GAAG,EAAEC,GAAG;EACZ,MAAMpD,IAAoB,GAAG,CAC3BkD,YAAY,GACRnJ,yBAAyB,CAACC,KAAK,EAAEkJ,YAAY,CAAC,GAC9C/T,WAAC,CAACuC,cAAc,CAAC,CAAC,EACtB+M,gBAAgB,EAChBwC,kBAAkB,CACnB;EAEkC;IACjC,IAAIzP,OAAO,KAAK,SAAS,EAAE;MACzBwO,IAAI,CAAC3K,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE4L,kBAAkB,EAAExC,gBAAgB,CAAC;IACzD;IACA,IACEjN,OAAO,KAAK,SAAS,IACpBA,OAAO,KAAK,SAAS,IAAI,CAACwI,KAAK,CAACqJ,eAAe,CAAC,gBAAgB,CAAE,EACnE;MACAF,GAAG,GAAGhU,WAAC,CAACmU,YAAY,CAAC,CAAC,GAAGpC,aAAa,EAAE,GAAGC,WAAW,CAAC,CAAC;MACxDiC,GAAG,GAAGjU,WAAC,CAACiG,cAAc,CACpB4E,KAAK,CAACC,SAAS,CAACzI,OAAO,KAAK,SAAS,GAAG,WAAW,GAAG,eAAe,CAAC,EACtEwO,IACF,CAAC;MACD,OAAO7Q,WAAC,CAAC8C,oBAAoB,CAAC,GAAG,EAAEkR,GAAG,EAAEC,GAAG,CAAC;IAC9C,CAAC,MAAM,IAAI5R,OAAO,KAAK,SAAS,EAAE;MAChC4R,GAAG,GAAGjU,WAAC,CAACiG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,gBAAgB,CAAC,EAAE+F,IAAI,CAAC;IACjE,CAAC,MAAM,IAAIxO,OAAO,KAAK,SAAS,EAAE;MAChC,IAAIyR,qBAAqB,EAAE;QACzBjD,IAAI,CAACvM,IAAI,CAAC2G,8BAA8B,CAAC6I,qBAAqB,CAAC,CAAC;MAClE;MACAG,GAAG,GAAGjU,WAAC,CAACiG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE+F,IAAI,CAAC;IAChE,CAAC,MAAM,IAAIxO,OAAO,KAAK,SAAS,EAAE;MAChC,IACEyR,qBAAqB,IACrB1S,UAAU,IACViO,oBAAoB,CAAC3N,KAAK,KAAK,CAAC,EAChC;QACAmP,IAAI,CAACvM,IAAI,CAAC+K,oBAAoB,CAAC;MACjC;MACA,IAAIyE,qBAAqB,EAAE;QACzBjD,IAAI,CAACvM,IAAI,CAAC2G,8BAA8B,CAAC6I,qBAAqB,CAAC,CAAC;MAClE,CAAC,MAAM,IAAI1S,UAAU,EAAE;QACrByP,IAAI,CAACvM,IAAI,CAACtE,WAAC,CAACoF,eAAe,CAAC,MAAM,EAAEpF,WAAC,CAAC4I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3D;MACA,IAAIxH,UAAU,EAAEyP,IAAI,CAACvM,IAAI,CAAClD,UAAU,CAAC;MACrC6S,GAAG,GAAGjU,WAAC,CAACiG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE+F,IAAI,CAAC;IAChE;EACF;EACA,IAAoCxO,OAAO,KAAK,SAAS,EAAE;IACzD,IACEyR,qBAAqB,IACrB1S,UAAU,IACViO,oBAAoB,CAAC3N,KAAK,KAAK,CAAC,EAChC;MACAmP,IAAI,CAACvM,IAAI,CAAC+K,oBAAoB,CAAC;IACjC;IACA,IAAIyE,qBAAqB,EAAE;MACzBjD,IAAI,CAACvM,IAAI,CAAC2G,8BAA8B,CAAC6I,qBAAqB,CAAC,CAAC;IAClE,CAAC,MAAM,IAAI1S,UAAU,EAAE;MACrByP,IAAI,CAACvM,IAAI,CAACtE,WAAC,CAACoF,eAAe,CAAC,MAAM,EAAEpF,WAAC,CAAC4I,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;IAC3D;IACA,IAAIxH,UAAU,EAAEyP,IAAI,CAACvM,IAAI,CAAClD,UAAU,CAAC;IACrC6S,GAAG,GAAGjU,WAAC,CAACiG,cAAc,CAAC4E,KAAK,CAACC,SAAS,CAAC,eAAe,CAAC,EAAE+F,IAAI,CAAC;EAChE;EAIA,IAAIkB,aAAa,CAACjT,MAAM,GAAG,CAAC,EAAE;IAC5B,IAAIkT,WAAW,CAAClT,MAAM,GAAG,CAAC,EAAE;MAC1BkV,GAAG,GAAGhU,WAAC,CAACoU,aAAa,CAAC,CACpBpU,WAAC,CAACqU,cAAc,CAACrU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAACmU,YAAY,CAACpC,aAAa,CAAC,CAAC,EAClE/R,WAAC,CAACqU,cAAc,CAACrU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAEF,WAAC,CAACmU,YAAY,CAACnC,WAAW,CAAC,CAAC,CACjE,CAAC;IACJ,CAAC,MAAM;MACLgC,GAAG,GAAGhU,WAAC,CAACmU,YAAY,CAACpC,aAAa,CAAC;MACnCkC,GAAG,GAAGjU,WAAC,CAAC2C,gBAAgB,CAACsR,GAAG,EAAEjU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;IAChE;EACF,CAAC,MAAM;IAEL8T,GAAG,GAAGhU,WAAC,CAACmU,YAAY,CAACnC,WAAW,CAAC;IACjCiC,GAAG,GAAGjU,WAAC,CAAC2C,gBAAgB,CAACsR,GAAG,EAAEjU,WAAC,CAACE,UAAU,CAAC,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC;EAChE;EAEA,OAAOF,WAAC,CAAC8C,oBAAoB,CAAC,GAAG,EAAEkR,GAAG,EAAEC,GAAG,CAAC;AAC9C;AAEA,SAASK,UAAUA,CACjB5U,IAAyE,EACzE;EACA,OAAOA,IAAI,CAACc,IAAI,KAAK,YAAY,GAC7Bd,IAAI,CAACC,IAAI,KAAK,WAAW,GACzBD,IAAI,CAACgC,KAAK,KAAK,WAAW;AAChC;AAEA,SAASsM,WAAWA,CAACtO,IAAuC,EAAE;EAC5D,OAAOA,IAAI,CAAC0I,UAAU,IAAI1I,IAAI,CAAC0I,UAAU,CAACtJ,MAAM,GAAG,CAAC;AACtD;AAEA,SAASyV,sBAAsBA,CAAC7U,IAAkB,EAAE;EAClD,QAAQA,IAAI,CAACc,IAAI;IACf,KAAK,uBAAuB;MAC1B,OAAO,IAAI;IACb,KAAK,aAAa;IAClB,KAAK,eAAe;IACpB,KAAK,oBAAoB;IACzB,KAAK,sBAAsB;MACzB,OAAOwN,WAAW,CAACtO,IAAI,CAAC;IAC1B;MACE,OAAO,KAAK;EAChB;AACF;AAEA,SAAS8U,oBAAoBA,CAAC9U,IAAa,EAAE;EAC3C,OAAOsO,WAAW,CAACtO,IAAI,CAAC,IAAIA,IAAI,CAAC2B,IAAI,CAACA,IAAI,CAACmH,IAAI,CAAC+L,sBAAsB,CAAC;AACzE;AAGA,SAASE,8BAA8BA,CACrCC,WAAwC,EACxCC,OASS,EACT;EACA,SAASC,sBAAsBA,CAC7BC,YAEC,EACDpT,GAAiB,EACjBoJ,KAAiB,EACe;IAChC,QAAQpJ,GAAG,CAACjB,IAAI;MACd,KAAK,eAAe;QAClB,OAAOR,WAAC,CAACgM,aAAa,CAACvK,GAAG,CAACC,KAAK,CAAC;MACnC,KAAK,gBAAgB;MACrB,KAAK,eAAe;QAAE;UACpB,MAAMoT,QAAQ,GAAGrT,GAAG,CAACC,KAAK,GAAG,EAAE;UAC/BmT,YAAY,CAAChU,GAAG,CAAC,KAAK,CAAC,CAACC,WAAW,CAACd,WAAC,CAACgM,aAAa,CAAC8I,QAAQ,CAAC,CAAC;UAC9D,OAAO9U,WAAC,CAACgM,aAAa,CAAC8I,QAAQ,CAAC;QAClC;MACA;QAAS;UACP,MAAMC,GAAG,GAAGF,YAAY,CAACtU,KAAK,CAAC6S,qBAAqB,CAAC3R,GAAG,CAAC;UACzDoT,YAAY,CACThU,GAAG,CAAC,KAAK,CAAC,CACVC,WAAW,CACVd,WAAC,CAAC8C,oBAAoB,CACpB,GAAG,EACHiS,GAAG,EACHhK,uBAAuB,CAACF,KAAK,EAAEpJ,GAAG,CACpC,CACF,CAAC;UACH,OAAOzB,WAAC,CAACe,SAAS,CAACgU,GAAG,CAAC;QACzB;IACF;EACF;EACA,OAAO;IACLC,kBAAkBA,CAACxV,IAAI,EAAEqL,KAAK,EAAE;MAC9B,MAAMjM,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACd,EAAE;MACvB,IAAIA,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM2E,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACqB,GAAG,CAAC,MAAM,CAAC,CAAC;QACjE,IAAI6T,WAAW,CAACvP,WAAW,CAAC,EAAE;UAC5B,MAAMxF,IAAI,GAAGf,EAAE,CAACe,IAAI;UACpBgV,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAElL,IAAI,CAAC;QACnC;MACF;IACF,CAAC;IACDsV,oBAAoBA,CAACzV,IAAI,EAAEqL,KAAK,EAAE;MAChC,MAAMjM,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACuE,IAAI;MACzB,IAAIrF,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM2E,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI6T,WAAW,CAACvP,WAAW,CAAC,EAAE;UAC5B,QAAQ3F,IAAI,CAACE,IAAI,CAACwV,QAAQ;YACxB,KAAK,GAAG;YACR,KAAK,KAAK;YACV,KAAK,KAAK;YACV,KAAK,KAAK;cACRP,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEjM,EAAE,CAACe,IAAI,CAAC;UACxC;QACF;MACF;IACF,CAAC;IACDwV,iBAAiBA,CAAC3V,IAAI,EAAEqL,KAAK,EAAE;MAC7B,MAAMjM,EAAE,GAAGY,IAAI,CAACE,IAAI,CAACuE,IAAI;MACzB,IAAIrF,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;QAC5B,MAAM2E,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;QAClE,IAAI6T,WAAW,CAACvP,WAAW,CAAC,EAAE;UAC5B,MAAMxF,IAAI,GAAGf,EAAE,CAACe,IAAI;UACpBgV,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAElL,IAAI,CAAC;QACnC;MACF;IACF,CAAC;IAGDyV,gBAAgBA,CAAC5V,IAAI,EAAEqL,KAAK,EAAE;MAC5B,KAAK,MAAMgK,YAAY,IAAIrV,IAAI,CAACqB,GAAG,CAAC,YAAY,CAAC,EAAE;QACjD,MAAM;UAAEnB;QAAK,CAAC,GAAGmV,YAAY;QAC7B,IAAInV,IAAI,CAACc,IAAI,KAAK,gBAAgB,EAAE;QACpC,MAAM5B,EAAE,GAAGc,IAAI,CAAC+B,GAAG;QACnB,MAAM0D,WAAW,GAAG,IAAA1B,oEAA2B,EAC7CoR,YAAY,CAAChU,GAAG,CAAC,OAAO,CAC1B,CAAC;QACD,IAAI6T,WAAW,CAACvP,WAAW,CAAC,EAAE;UAC5B,IAAI,CAACzF,IAAI,CAACqM,QAAQ,EAAE;YAElB,IAAI,CAACuI,UAAU,CAAC1V,EAAoC,CAAC,EAAE;cACrD,IAAIA,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;gBAC5BmU,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEjM,EAAE,CAACe,IAAI,CAAC;cACtC,CAAC,MAAM;gBACL,MAAMW,SAAS,GAAGN,WAAC,CAACgM,aAAa,CAC9BpN,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;gBACDiT,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEvK,SAAS,CAAC;cACxC;YACF;UACF,CAAC,MAAM;YACL,MAAMyU,GAAG,GAAGH,sBAAsB,CAChCC,YAAY,EAEZjW,EAAE,EACFiM,KACF,CAAC;YACD8J,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEkK,GAAG,CAAC;UAClC;QACF;MACF;IACF,CAAC;IACD7G,oBAAoBA,CAAC1O,IAAI,EAAEqL,KAAK,EAAE;MAChC,MAAM;QAAEnL;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAM2F,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAI6T,WAAW,CAACvP,WAAW,CAAC,EAAE;QAC5B,MAAM7E,SAAS,GAAGN,WAAC,CAACgM,aAAa,CAAC,GAAG,GAAGtM,IAAI,CAAC+B,GAAG,CAAC7C,EAAE,CAACe,IAAI,CAAC;QACzDgV,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEvK,SAAS,CAAC;MACxC;IACF,CAAC;IACD6N,qBAAqBA,CAAC3O,IAAI,EAAEqL,KAAK,EAAE;MACjC,MAAM;QAAEnL;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAMZ,EAAE,GAAGc,IAAI,CAAC+B,GAAG;MACnB,MAAM0D,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAI6T,WAAW,CAACvP,WAAW,CAAC,EAAE;QAC5B,IAAI,CAACzF,IAAI,CAACqM,QAAQ,EAAE;UAClB,IAAInN,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;YAC5BmU,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEjM,EAAE,CAACe,IAAI,CAAC;UACtC,CAAC,MAAM,IAAIf,EAAE,CAAC4B,IAAI,KAAK,aAAa,EAAE;YACpC,MAAMF,SAAS,GAAGN,WAAC,CAACgM,aAAa,CAAC,GAAG,GAAGpN,EAAE,CAACA,EAAE,CAACe,IAAI,CAAC;YACnDgV,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEvK,SAAS,CAAC;UACxC,CAAC,MAAM;YACL,MAAMA,SAAS,GAAGN,WAAC,CAACgM,aAAa,CAC9BpN,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;YACDiT,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEvK,SAAS,CAAC;UACxC;QACF,CAAC,MAAM;UACL,MAAMyU,GAAG,GAAGH,sBAAsB,CAChCpV,IAAI,EAEJZ,EAAE,EACFiM,KACF,CAAC;UACD8J,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEkK,GAAG,CAAC;QAClC;MACF;IACF,CAAC;IACD9G,aAAaA,CAACzO,IAAI,EAAEqL,KAAK,EAAE;MACzB,MAAM;QAAEnL;MAAK,CAAC,GAAGF,IAAI;MACrB,MAAMZ,EAAE,GAAGc,IAAI,CAAC+B,GAAG;MACnB,MAAM0D,WAAW,GAAG,IAAA1B,oEAA2B,EAACjE,IAAI,CAACqB,GAAG,CAAC,OAAO,CAAC,CAAC;MAClE,IAAI6T,WAAW,CAACvP,WAAW,CAAC,EAAE;QAC5B,IAAI,CAACzF,IAAI,CAACqM,QAAQ,EAAE;UAClB,IAAInN,EAAE,CAAC4B,IAAI,KAAK,YAAY,EAAE;YAC5BmU,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEjM,EAAE,CAACe,IAAI,CAAC;UACtC,CAAC,MAAM;YACL,MAAMW,SAAS,GAAGN,WAAC,CAACgM,aAAa,CAC9BpN,EAAE,CACA8C,KAAK,GAAG,EACb,CAAC;YACDiT,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEvK,SAAS,CAAC;UACxC;QACF,CAAC,MAAM;UACL,MAAMyU,GAAG,GAAGH,sBAAsB,CAACpV,IAAI,EAAEZ,EAAE,EAAEiM,KAAK,CAAC;UACnD8J,OAAO,CAACxP,WAAW,EAAE0F,KAAK,EAAEkK,GAAG,CAAC;QAClC;MACF;IACF;EACF,CAAC;AACH;AAEA,SAASM,mCAAmCA,CAAC7V,IAAc,EAAE;EAC3D,OACEA,IAAI,CAAC8V,iBAAiB,CAAC;IAAE1W,EAAE,EAAE;EAAK,CAAC,CAAC,IAAI4V,oBAAoB,CAAChV,IAAI,CAACE,IAAI,CAAC;AAE3E;AAEA,SAASsB,wBAAwBA,CAACT,KAAY,EAAEZ,IAAY,EAAE;EAC5D,MAAMf,EAAE,GAAG2B,KAAK,CAACsP,qBAAqB,CAAClQ,IAAI,CAAC;EAC5CY,KAAK,CAAC+D,IAAI,CAAC;IAAE1F,EAAE;IAAEkI,IAAI,EAAE;EAAM,CAAC,CAAC;EAC/B,OAAO9G,WAAC,CAACe,SAAS,CAACnC,EAAE,CAAC;AACxB;AAEe,SAAA2W,SACb;EAAEC,aAAa;EAAEC;AAAsB,CAAC,EACxC;EAAEC;AAAe,CAAC,EAClBrT,OAA6B,EAC7BsT,QAAkC,EACpB;EAAA,IAAAC,WAAA,EAAAC,YAAA;EAGP;IACL,IACExT,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,IACrBA,OAAO,KAAK,SAAS,EACrB;MACAmT,aAAa,CAAkB,SAAU,CAAC;IAC5C,CAAC,MAAM,IAAInT,OAAO,KAAK,SAAS,EAAE;MAChCmT,aAAa,CAAkB,SAAU,CAAC;IAC5C,CAAC,MAAM;MACLA,aAAa,CAAkB,SAAU,CAAC;IAC5C;EACF;EAEA,MAAMM,OAAO,GAAG,IAAIC,OAAO,CAAW,CAAC;EACvC,MAAMjJ,aAAa,IAAA8I,WAAA,GAAGH,UAAU,CAAC,eAAe,CAAC,YAAAG,WAAA,GAAIF,KAAK;EAC1D,MAAM3I,oBAAoB,IAAA8I,YAAA,GAAGJ,UAAU,CAAC,sBAAsB,CAAC,YAAAI,YAAA,GAAIH,KAAK;EAExE,MAAMM,sBAA2C,GAC/CvB,8BAA8B,CAC5BY,mCAAmC,EACnCY,UACF,CAAC;EAEH,SAASA,UAAUA,CACjBzW,IAAuB,EACvBqL,KAAiB,EACjBvK,SAA8D,EAC9D;IAAA,IAAA4V,UAAA,EAAAC,QAAA;IACA,IAAIL,OAAO,CAAC/V,GAAG,CAACP,IAAI,CAAC,EAAE;IACvB,MAAM;MAAEE;IAAK,CAAC,GAAGF,IAAI;IACrB,CAAA0W,UAAA,GAAA5V,SAAS,YAAA4V,UAAA,GAAT5V,SAAS,IAAA6V,QAAA,GAAKzW,IAAI,CAACd,EAAE,qBAAPuX,QAAA,CAASxW,IAAI;IAC3B,MAAM2B,OAAO,GAAGuL,cAAc,CAC5BrN,IAAI,EACJqL,KAAK,EACLiC,aAAa,EACbC,oBAAoB,EACpBzM,SAAS,EACT0V,sBAAsB,EACtB3T,OACF,CAAC;IACD,IAAIf,OAAO,EAAE;MACXwU,OAAO,CAACrW,GAAG,CAAC6B,OAAO,CAAC;MACpB;IACF;IACAwU,OAAO,CAACrW,GAAG,CAACD,IAAI,CAAC;EACnB;EAEA,OAAO;IACLG,IAAI,EAAE,qBAAqB;IAC3BgW,QAAQ,EAAEA,QAAQ;IAElBhB,OAAO,EAAAyB,MAAA,CAAAC,MAAA;MACLC,wBAAwBA,CAAC9W,IAAI,EAAEqL,KAAK,EAAE;QACpC,MAAM;UAAE0L;QAAY,CAAC,GAAG/W,IAAI,CAACE,IAAI;QACjC,IACE,CAAA6W,WAAW,oBAAXA,WAAW,CAAE/V,IAAI,MAAK,kBAAkB,IAGxCwN,WAAW,CAACuI,WAAW,CAAC,EACxB;UACA,MAAM7B,WAAW,GAAG,CAAC6B,WAAW,CAAC3X,EAAE;UACnC,MAAM4X,yBAAyB,GAAG,IAAAC,qCAAsB,EACtDjX,IACF,CAA4C;UAC5C,IAAIkV,WAAW,EAAE;YACfuB,UAAU,CACRO,yBAAyB,EACzB3L,KAAK,EACL7K,WAAC,CAACgM,aAAa,CAAC,SAAS,CAC3B,CAAC;UACH;QACF;MACF,CAAC;MACD0K,sBAAsBA,CAAClX,IAAI,EAAE;QAC3B,MAAM;UAAE+W;QAAY,CAAC,GAAG/W,IAAI,CAACE,IAAI;QACjC,IACE,CAAA6W,WAAW,oBAAXA,WAAW,CAAE/V,IAAI,MAAK,kBAAkB,IAGxCwN,WAAW,CAACuI,WAAW,CAAC,EACxB;UACA,IAAAE,qCAAsB,EAACjX,IAAI,CAAC;QAC9B;MACF,CAAC;MAEDmX,KAAKA,CAACnX,IAAI,EAAEqL,KAAK,EAAE;QACjBoL,UAAU,CAACzW,IAAI,EAAEqL,KAAK,EAAEhJ,SAAS,CAAC;MACpC;IAAC,GAEEmU,sBAAsB;EAE7B,CAAC;AACH", "ignoreList": []}