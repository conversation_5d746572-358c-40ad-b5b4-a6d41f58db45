{"version": 3, "names": ["_assert", "require", "_core", "_helperSimpleAccess", "assignmentExpression", "cloneNode", "expressionStatement", "getOuterBindingIdentifiers", "identifier", "isArrowFunctionExpression", "isClassExpression", "isFunctionExpression", "isIdentifier", "isMemberExpression", "isVariableDeclaration", "jsxIdentifier", "jsxMemberExpression", "memberExpression", "numericLiteral", "sequenceExpression", "stringLiteral", "variableDeclaration", "variableDeclarator", "t", "isInType", "path", "parent", "type", "parentPath", "exportKind", "isStatement", "isExpression", "rewriteLiveReferences", "programPath", "metadata", "wrapReference", "imported", "Map", "exported", "requeueInParent", "requeue", "source", "data", "localName", "importName", "imports", "set", "importsNamespace", "local", "exportMeta", "get", "push", "names", "rewriteBindingInitVisitorState", "scope", "traverse", "rewriteBindingInitVisitor", "bindingNames", "Set", "Array", "from", "keys", "simplifyAccess", "rewriteReferencesVisitorState", "seen", "WeakSet", "buildImportReference", "identNode", "meta", "referenced", "wrap", "_wrapReference", "namespace", "name", "_wrapReference2", "interop", "computed", "stringSpecifiers", "has", "rewriteReferencesVisitor", "<PERSON><PERSON>", "skip", "ClassDeclaration", "id", "node", "Error", "exportNames", "length", "statement", "buildBindingExportAssignmentExpression", "_blockHoist", "insertAfter", "VariableDeclaration", "isVar", "kind", "decl", "init", "buildUndefinedNode", "Object", "localExpr", "exportsObjectName", "exportName", "currentScope", "hasOwnBinding", "rename", "reduce", "expr", "buildImportThrow", "template", "expression", "ast", "ReferencedIdentifier", "add", "importData", "buildCodeFrameError", "localBinding", "getBinding", "rootBinding", "ref", "loc", "isCallExpression", "callee", "isOptionalCallExpression", "isTaggedTemplateExpression", "tag", "replaceWith", "isJSXIdentifier", "object", "property", "UpdateExpression", "arg", "update", "exportedNames", "operator", "prefix", "generateDeclaredUidIdentifier", "AssignmentExpression", "exit", "left", "assert", "assignment", "right", "ids", "programScopeIds", "filter", "find", "items", "for<PERSON>ach", "isExpressionStatement", "ForOfStatement|ForInStatement", "programScope", "didTransformExport", "importConstViolationName", "loopBodyScope", "ensureBlock", "bodyPath", "newLoopId", "generateUidIdentifierBasedOnNode", "registerDeclaration", "unshiftContainer"], "sources": ["../src/rewrite-live-references.ts"], "sourcesContent": ["import assert from \"assert\";\nimport { template, types as t } from \"@babel/core\";\nimport type { NodeP<PERSON>, Visitor, Scope } from \"@babel/traverse\";\nimport simplifyAccess from \"@babel/helper-simple-access\";\n\nimport type { ModuleMetadata } from \"./normalize-and-load-metadata.ts\";\n\nconst {\n  assignmentExpression,\n  cloneNode,\n  expressionStatement,\n  getOuterBindingIdentifiers,\n  identifier,\n  isArrowFunctionExpression,\n  isClassExpression,\n  isFunctionExpression,\n  isIdentifier,\n  isMemberExpression,\n  isVariableDeclaration,\n  jsxIdentifier,\n  jsxMemberExpression,\n  memberExpression,\n  numericLiteral,\n  sequenceExpression,\n  stringLiteral,\n  variableDeclaration,\n  variableDeclarator,\n} = t;\n\ninterface RewriteReferencesVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n  imported: Map<any, any>;\n  buildImportReference: (\n    [source, importName, localName]: readonly [string, string, string],\n    identNode: t.Identifier | t.CallExpression | t.JSXIdentifier,\n  ) => any;\n  seen: WeakSet<object>;\n}\n\ninterface RewriteBindingInitVisitorState {\n  exported: Map<any, any>;\n  metadata: ModuleMetadata;\n  requeueInParent: (path: NodePath) => void;\n  scope: Scope;\n}\n\nfunction isInType(path: NodePath) {\n  do {\n    switch (path.parent.type) {\n      case \"TSTypeAnnotation\":\n      case \"TSTypeAliasDeclaration\":\n      case \"TSTypeReference\":\n      case \"TypeAnnotation\":\n      case \"TypeAlias\":\n        return true;\n      case \"ExportSpecifier\":\n        return (\n          (\n            path.parentPath.parent as\n              | t.ExportDefaultDeclaration\n              | t.ExportNamedDeclaration\n          ).exportKind === \"type\"\n        );\n      default:\n        if (path.parentPath.isStatement() || path.parentPath.isExpression()) {\n          return false;\n        }\n    }\n  } while ((path = path.parentPath));\n}\n\nexport default function rewriteLiveReferences(\n  programPath: NodePath<t.Program>,\n  metadata: ModuleMetadata,\n  wrapReference: (ref: t.Expression, payload: unknown) => null | t.Expression,\n) {\n  const imported = new Map();\n  const exported = new Map();\n  const requeueInParent = (path: NodePath) => {\n    // Manually re-queue `exports.default =` expressions so that the ES3\n    // transform has an opportunity to convert them. Ideally this would\n    // happen automatically from the replaceWith above. See #4140 for\n    // more info.\n    programPath.requeue(path);\n  };\n\n  for (const [source, data] of metadata.source) {\n    for (const [localName, importName] of data.imports) {\n      imported.set(localName, [source, importName, null]);\n    }\n    for (const localName of data.importsNamespace) {\n      imported.set(localName, [source, null, localName]);\n    }\n  }\n\n  for (const [local, data] of metadata.local) {\n    let exportMeta = exported.get(local);\n    if (!exportMeta) {\n      exportMeta = [];\n      exported.set(local, exportMeta);\n    }\n\n    exportMeta.push(...data.names);\n  }\n\n  // Rewrite initialization of bindings to update exports.\n  const rewriteBindingInitVisitorState: RewriteBindingInitVisitorState = {\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    exported, // local name => exported name list\n  };\n  programPath.traverse(\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    rewriteBindingInitVisitor,\n    rewriteBindingInitVisitorState,\n  );\n\n  // NOTE(logan): The 'Array.from' calls are to make this code with in loose mode.\n  const bindingNames = new Set([\n    ...Array.from(imported.keys()),\n    ...Array.from(exported.keys()),\n  ]);\n  if (process.env.BABEL_8_BREAKING) {\n    simplifyAccess(programPath, bindingNames);\n  } else {\n    // @ts-ignore(Babel 7 vs Babel 8) The third param has been removed in Babel 8.\n    simplifyAccess(programPath, bindingNames, false);\n  }\n\n  // Rewrite reads/writes from imports and exports to have the correct behavior.\n  const rewriteReferencesVisitorState: RewriteReferencesVisitorState = {\n    seen: new WeakSet(),\n    metadata,\n    requeueInParent,\n    scope: programPath.scope,\n    imported, // local / import\n    exported, // local name => exported name list\n    buildImportReference([source, importName, localName], identNode) {\n      const meta = metadata.source.get(source);\n      meta.referenced = true;\n\n      if (localName) {\n        if (meta.wrap) {\n          // @ts-expect-error Fixme: we should handle the case when identNode is a JSXIdentifier\n          identNode = wrapReference(identNode, meta.wrap) ?? identNode;\n        }\n        return identNode;\n      }\n\n      let namespace: t.Expression = identifier(meta.name);\n      if (meta.wrap) {\n        namespace = wrapReference(namespace, meta.wrap) ?? namespace;\n      }\n\n      if (importName === \"default\" && meta.interop === \"node-default\") {\n        return namespace;\n      }\n\n      const computed = metadata.stringSpecifiers.has(importName);\n\n      return memberExpression(\n        namespace,\n        computed ? stringLiteral(importName) : identifier(importName),\n        computed,\n      );\n    },\n  };\n  // eslint-disable-next-line @typescript-eslint/no-use-before-define\n  programPath.traverse(rewriteReferencesVisitor, rewriteReferencesVisitorState);\n}\n\n/**\n * A visitor to inject export update statements during binding initialization.\n */\nconst rewriteBindingInitVisitor: Visitor<RewriteBindingInitVisitorState> = {\n  Scope(path) {\n    path.skip();\n  },\n  ClassDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    const { id } = path.node;\n    if (!id) throw new Error(\"Expected class to have a name\");\n    const localName = id.name;\n\n    const exportNames = exported.get(localName) || [];\n    if (exportNames.length > 0) {\n      const statement = expressionStatement(\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        buildBindingExportAssignmentExpression(\n          metadata,\n          exportNames,\n          identifier(localName),\n          path.scope,\n        ),\n      );\n      // @ts-expect-error todo(flow->ts): avoid mutations\n      statement._blockHoist = path.node._blockHoist;\n\n      requeueInParent(path.insertAfter(statement)[0]);\n    }\n  },\n  VariableDeclaration(path) {\n    const { requeueInParent, exported, metadata } = this;\n\n    const isVar = path.node.kind === \"var\";\n\n    for (const decl of path.get(\"declarations\")) {\n      const { id } = decl.node;\n      let { init } = decl.node;\n      if (\n        isIdentifier(id) &&\n        exported.has(id.name) &&\n        !isArrowFunctionExpression(init) &&\n        (!isFunctionExpression(init) || init.id) &&\n        (!isClassExpression(init) || init.id)\n      ) {\n        if (!init) {\n          if (isVar) {\n            // This variable might have already been assigned to, and the\n            // uninitalized declaration doesn't set it to `undefined` and does\n            // not updated the exported value.\n            continue;\n          } else {\n            init = path.scope.buildUndefinedNode();\n          }\n        }\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        decl.node.init = buildBindingExportAssignmentExpression(\n          metadata,\n          exported.get(id.name),\n          init,\n          path.scope,\n        );\n        requeueInParent(decl.get(\"init\"));\n      } else {\n        for (const localName of Object.keys(\n          decl.getOuterBindingIdentifiers(),\n        )) {\n          if (exported.has(localName)) {\n            const statement = expressionStatement(\n              // eslint-disable-next-line @typescript-eslint/no-use-before-define\n              buildBindingExportAssignmentExpression(\n                metadata,\n                exported.get(localName),\n                identifier(localName),\n                path.scope,\n              ),\n            );\n            // @ts-expect-error todo(flow->ts): avoid mutations\n            statement._blockHoist = path.node._blockHoist;\n\n            requeueInParent(path.insertAfter(statement)[0]);\n          }\n        }\n      }\n    }\n  },\n};\n\nconst buildBindingExportAssignmentExpression = (\n  metadata: ModuleMetadata,\n  exportNames: string[],\n  localExpr: t.Expression,\n  scope: Scope,\n) => {\n  const exportsObjectName = metadata.exportName;\n  for (\n    let currentScope = scope;\n    currentScope != null;\n    currentScope = currentScope.parent\n  ) {\n    if (currentScope.hasOwnBinding(exportsObjectName)) {\n      currentScope.rename(exportsObjectName);\n    }\n  }\n  return (exportNames || []).reduce((expr, exportName) => {\n    // class Foo {} export { Foo, Foo as Bar };\n    // as\n    // class Foo {} exports.Foo = exports.Bar = Foo;\n    const { stringSpecifiers } = metadata;\n    const computed = stringSpecifiers.has(exportName);\n    return assignmentExpression(\n      \"=\",\n      memberExpression(\n        identifier(exportsObjectName),\n        computed ? stringLiteral(exportName) : identifier(exportName),\n        /* computed */ computed,\n      ),\n      expr,\n    );\n  }, localExpr);\n};\n\nconst buildImportThrow = (localName: string) => {\n  return template.expression.ast`\n    (function() {\n      throw new Error('\"' + '${localName}' + '\" is read-only.');\n    })()\n  `;\n};\n\nconst rewriteReferencesVisitor: Visitor<RewriteReferencesVisitorState> = {\n  ReferencedIdentifier(path) {\n    const { seen, buildImportReference, scope, imported, requeueInParent } =\n      this;\n    if (seen.has(path.node)) return;\n    seen.add(path.node);\n\n    const localName = path.node.name;\n\n    const importData = imported.get(localName);\n    if (importData) {\n      if (isInType(path)) {\n        throw path.buildCodeFrameError(\n          `Cannot transform the imported binding \"${localName}\" since it's also used in a type annotation. ` +\n            `Please strip type annotations using @babel/preset-typescript or @babel/preset-flow.`,\n        );\n      }\n\n      const localBinding = path.scope.getBinding(localName);\n      const rootBinding = scope.getBinding(localName);\n\n      // redeclared in this scope\n      if (rootBinding !== localBinding) return;\n\n      const ref = buildImportReference(importData, path.node);\n\n      // Preserve the binding location so that sourcemaps are nicer.\n      ref.loc = path.node.loc;\n\n      if (\n        (path.parentPath.isCallExpression({ callee: path.node }) ||\n          path.parentPath.isOptionalCallExpression({ callee: path.node }) ||\n          path.parentPath.isTaggedTemplateExpression({ tag: path.node })) &&\n        isMemberExpression(ref)\n      ) {\n        path.replaceWith(sequenceExpression([numericLiteral(0), ref]));\n      } else if (path.isJSXIdentifier() && isMemberExpression(ref)) {\n        const { object, property } = ref;\n        path.replaceWith(\n          jsxMemberExpression(\n            // @ts-expect-error todo(flow->ts): possible bug `object` might not have a name\n            jsxIdentifier(object.name),\n            // @ts-expect-error todo(flow->ts): possible bug `property` might not have a name\n            jsxIdentifier(property.name),\n          ),\n        );\n      } else {\n        path.replaceWith(ref);\n      }\n\n      requeueInParent(path);\n\n      // The path could have been replaced with an identifier that would\n      // otherwise be re-visited, so we skip processing its children.\n      path.skip();\n    }\n  },\n\n  UpdateExpression(path) {\n    const {\n      scope,\n      seen,\n      imported,\n      exported,\n      requeueInParent,\n      buildImportReference,\n    } = this;\n\n    if (seen.has(path.node)) return;\n\n    seen.add(path.node);\n\n    const arg = path.get(\"argument\");\n\n    // No change needed\n    if (arg.isMemberExpression()) return;\n\n    const update = path.node;\n\n    if (arg.isIdentifier()) {\n      const localName = arg.node.name;\n\n      // redeclared in this scope\n      if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n        return;\n      }\n\n      const exportedNames = exported.get(localName);\n      const importData = imported.get(localName);\n\n      if (exportedNames?.length > 0 || importData) {\n        if (importData) {\n          path.replaceWith(\n            assignmentExpression(\n              update.operator[0] + \"=\",\n              buildImportReference(importData, arg.node),\n              buildImportThrow(localName),\n            ),\n          );\n        } else if (update.prefix) {\n          // ++foo\n          // =>   exports.foo = ++foo\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              cloneNode(update),\n              path.scope,\n            ),\n          );\n        } else {\n          // foo++\n          // =>   (ref = i++, exports.i = i, ref)\n          const ref = scope.generateDeclaredUidIdentifier(localName);\n\n          path.replaceWith(\n            sequenceExpression([\n              assignmentExpression(\"=\", cloneNode(ref), cloneNode(update)),\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                identifier(localName),\n                path.scope,\n              ),\n              cloneNode(ref),\n            ]),\n          );\n        }\n      }\n    }\n\n    requeueInParent(path);\n    path.skip();\n  },\n\n  AssignmentExpression: {\n    exit(path) {\n      const {\n        scope,\n        seen,\n        imported,\n        exported,\n        requeueInParent,\n        buildImportReference,\n      } = this;\n\n      if (seen.has(path.node)) return;\n      seen.add(path.node);\n\n      const left = path.get(\"left\");\n\n      // No change needed\n      if (left.isMemberExpression()) return;\n\n      if (left.isIdentifier()) {\n        // Simple update-assign foo += 1; export { foo };\n        // =>   exports.foo =  (foo += 1);\n        const localName = left.node.name;\n\n        // redeclared in this scope\n        if (scope.getBinding(localName) !== path.scope.getBinding(localName)) {\n          return;\n        }\n\n        const exportedNames = exported.get(localName);\n        const importData = imported.get(localName);\n        if (exportedNames?.length > 0 || importData) {\n          assert(path.node.operator === \"=\", \"Path was not simplified\");\n\n          const assignment = path.node;\n\n          if (importData) {\n            assignment.left = buildImportReference(importData, left.node);\n\n            assignment.right = sequenceExpression([\n              assignment.right,\n              buildImportThrow(localName),\n            ]);\n          }\n\n          path.replaceWith(\n            buildBindingExportAssignmentExpression(\n              this.metadata,\n              exportedNames,\n              assignment,\n              path.scope,\n            ),\n          );\n          requeueInParent(path);\n        }\n      } else {\n        const ids = left.getOuterBindingIdentifiers();\n        const programScopeIds = Object.keys(ids).filter(\n          localName =>\n            scope.getBinding(localName) === path.scope.getBinding(localName),\n        );\n        const id = programScopeIds.find(localName => imported.has(localName));\n\n        if (id) {\n          path.node.right = sequenceExpression([\n            path.node.right,\n            buildImportThrow(id),\n          ]);\n        }\n\n        // Complex ({a, b, c} = {}); export { a, c };\n        // =>   ({a, b, c} = {}), (exports.a = a, exports.c = c);\n        const items: t.Expression[] = [];\n        programScopeIds.forEach(localName => {\n          const exportedNames = exported.get(localName) || [];\n          if (exportedNames.length > 0) {\n            items.push(\n              buildBindingExportAssignmentExpression(\n                this.metadata,\n                exportedNames,\n                identifier(localName),\n                path.scope,\n              ),\n            );\n          }\n        });\n\n        if (items.length > 0) {\n          let node: t.Node = sequenceExpression(items);\n          if (path.parentPath.isExpressionStatement()) {\n            node = expressionStatement(node);\n            // @ts-expect-error todo(flow->ts): avoid mutations\n            node._blockHoist = path.parentPath.node._blockHoist;\n          }\n\n          const statement = path.insertAfter(node)[0];\n          requeueInParent(statement);\n        }\n      }\n    },\n  },\n  \"ForOfStatement|ForInStatement\"(\n    path: NodePath<t.ForOfStatement | t.ForInStatement>,\n  ) {\n    const { scope, node } = path;\n    const { left } = node;\n    const { exported, imported, scope: programScope } = this;\n\n    if (!isVariableDeclaration(left)) {\n      let didTransformExport = false,\n        importConstViolationName;\n      const loopBodyScope = path.get(\"body\").scope;\n      for (const name of Object.keys(getOuterBindingIdentifiers(left))) {\n        if (programScope.getBinding(name) === scope.getBinding(name)) {\n          if (exported.has(name)) {\n            didTransformExport = true;\n            if (loopBodyScope.hasOwnBinding(name)) {\n              loopBodyScope.rename(name);\n            }\n          }\n          if (imported.has(name) && !importConstViolationName) {\n            importConstViolationName = name;\n          }\n        }\n      }\n      if (!didTransformExport && !importConstViolationName) {\n        return;\n      }\n\n      path.ensureBlock();\n      const bodyPath = path.get(\"body\");\n\n      const newLoopId = scope.generateUidIdentifierBasedOnNode(left);\n      path\n        .get(\"left\")\n        .replaceWith(\n          variableDeclaration(\"let\", [\n            variableDeclarator(cloneNode(newLoopId)),\n          ]),\n        );\n      scope.registerDeclaration(path.get(\"left\"));\n\n      if (didTransformExport) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          expressionStatement(assignmentExpression(\"=\", left, newLoopId)),\n        );\n      }\n      if (importConstViolationName) {\n        bodyPath.unshiftContainer(\n          \"body\",\n          expressionStatement(buildImportThrow(importConstViolationName)),\n        );\n      }\n    }\n  },\n};\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAEA,IAAAE,mBAAA,GAAAF,OAAA;AAIA,MAAM;EACJG,oBAAoB;EACpBC,SAAS;EACTC,mBAAmB;EACnBC,0BAA0B;EAC1BC,UAAU;EACVC,yBAAyB;EACzBC,iBAAiB;EACjBC,oBAAoB;EACpBC,YAAY;EACZC,kBAAkB;EAClBC,qBAAqB;EACrBC,aAAa;EACbC,mBAAmB;EACnBC,gBAAgB;EAChBC,cAAc;EACdC,kBAAkB;EAClBC,aAAa;EACbC,mBAAmB;EACnBC;AACF,CAAC,GAAGC,WAAC;AAsBL,SAASC,QAAQA,CAACC,IAAc,EAAE;EAChC,GAAG;IACD,QAAQA,IAAI,CAACC,MAAM,CAACC,IAAI;MACtB,KAAK,kBAAkB;MACvB,KAAK,wBAAwB;MAC7B,KAAK,iBAAiB;MACtB,KAAK,gBAAgB;MACrB,KAAK,WAAW;QACd,OAAO,IAAI;MACb,KAAK,iBAAiB;QACpB,OAEIF,IAAI,CAACG,UAAU,CAACF,MAAM,CAGtBG,UAAU,KAAK,MAAM;MAE3B;QACE,IAAIJ,IAAI,CAACG,UAAU,CAACE,WAAW,CAAC,CAAC,IAAIL,IAAI,CAACG,UAAU,CAACG,YAAY,CAAC,CAAC,EAAE;UACnE,OAAO,KAAK;QACd;IACJ;EACF,CAAC,QAASN,IAAI,GAAGA,IAAI,CAACG,UAAU;AAClC;AAEe,SAASI,qBAAqBA,CAC3CC,WAAgC,EAChCC,QAAwB,EACxBC,aAA2E,EAC3E;EACA,MAAMC,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;EAC1B,MAAMC,QAAQ,GAAG,IAAID,GAAG,CAAC,CAAC;EAC1B,MAAME,eAAe,GAAId,IAAc,IAAK;IAK1CQ,WAAW,CAACO,OAAO,CAACf,IAAI,CAAC;EAC3B,CAAC;EAED,KAAK,MAAM,CAACgB,MAAM,EAAEC,IAAI,CAAC,IAAIR,QAAQ,CAACO,MAAM,EAAE;IAC5C,KAAK,MAAM,CAACE,SAAS,EAAEC,UAAU,CAAC,IAAIF,IAAI,CAACG,OAAO,EAAE;MAClDT,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAEG,UAAU,EAAE,IAAI,CAAC,CAAC;IACrD;IACA,KAAK,MAAMD,SAAS,IAAID,IAAI,CAACK,gBAAgB,EAAE;MAC7CX,QAAQ,CAACU,GAAG,CAACH,SAAS,EAAE,CAACF,MAAM,EAAE,IAAI,EAAEE,SAAS,CAAC,CAAC;IACpD;EACF;EAEA,KAAK,MAAM,CAACK,KAAK,EAAEN,IAAI,CAAC,IAAIR,QAAQ,CAACc,KAAK,EAAE;IAC1C,IAAIC,UAAU,GAAGX,QAAQ,CAACY,GAAG,CAACF,KAAK,CAAC;IACpC,IAAI,CAACC,UAAU,EAAE;MACfA,UAAU,GAAG,EAAE;MACfX,QAAQ,CAACQ,GAAG,CAACE,KAAK,EAAEC,UAAU,CAAC;IACjC;IAEAA,UAAU,CAACE,IAAI,CAAC,GAAGT,IAAI,CAACU,KAAK,CAAC;EAChC;EAGA,MAAMC,8BAA8D,GAAG;IACrEnB,QAAQ;IACRK,eAAe;IACfe,KAAK,EAAErB,WAAW,CAACqB,KAAK;IACxBhB;EACF,CAAC;EACDL,WAAW,CAACsB,QAAQ,CAElBC,yBAAyB,EACzBH,8BACF,CAAC;EAGD,MAAMI,YAAY,GAAG,IAAIC,GAAG,CAAC,CAC3B,GAAGC,KAAK,CAACC,IAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,CAAC,CAAC,EAC9B,GAAGF,KAAK,CAACC,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAAC,CAAC,CAAC,CAC/B,CAAC;EAGK;IAEL,IAAAC,2BAAc,EAAC7B,WAAW,EAAEwB,YAAY,EAAE,KAAK,CAAC;EAClD;EAGA,MAAMM,6BAA4D,GAAG;IACnEC,IAAI,EAAE,IAAIC,OAAO,CAAC,CAAC;IACnB/B,QAAQ;IACRK,eAAe;IACfe,KAAK,EAAErB,WAAW,CAACqB,KAAK;IACxBlB,QAAQ;IACRE,QAAQ;IACR4B,oBAAoBA,CAAC,CAACzB,MAAM,EAAEG,UAAU,EAAED,SAAS,CAAC,EAAEwB,SAAS,EAAE;MAC/D,MAAMC,IAAI,GAAGlC,QAAQ,CAACO,MAAM,CAACS,GAAG,CAACT,MAAM,CAAC;MACxC2B,IAAI,CAACC,UAAU,GAAG,IAAI;MAEtB,IAAI1B,SAAS,EAAE;QACb,IAAIyB,IAAI,CAACE,IAAI,EAAE;UAAA,IAAAC,cAAA;UAEbJ,SAAS,IAAAI,cAAA,GAAGpC,aAAa,CAACgC,SAAS,EAAEC,IAAI,CAACE,IAAI,CAAC,YAAAC,cAAA,GAAIJ,SAAS;QAC9D;QACA,OAAOA,SAAS;MAClB;MAEA,IAAIK,SAAuB,GAAGhE,UAAU,CAAC4D,IAAI,CAACK,IAAI,CAAC;MACnD,IAAIL,IAAI,CAACE,IAAI,EAAE;QAAA,IAAAI,eAAA;QACbF,SAAS,IAAAE,eAAA,GAAGvC,aAAa,CAACqC,SAAS,EAAEJ,IAAI,CAACE,IAAI,CAAC,YAAAI,eAAA,GAAIF,SAAS;MAC9D;MAEA,IAAI5B,UAAU,KAAK,SAAS,IAAIwB,IAAI,CAACO,OAAO,KAAK,cAAc,EAAE;QAC/D,OAAOH,SAAS;MAClB;MAEA,MAAMI,QAAQ,GAAG1C,QAAQ,CAAC2C,gBAAgB,CAACC,GAAG,CAAClC,UAAU,CAAC;MAE1D,OAAO3B,gBAAgB,CACrBuD,SAAS,EACTI,QAAQ,GAAGxD,aAAa,CAACwB,UAAU,CAAC,GAAGpC,UAAU,CAACoC,UAAU,CAAC,EAC7DgC,QACF,CAAC;IACH;EACF,CAAC;EAED3C,WAAW,CAACsB,QAAQ,CAACwB,wBAAwB,EAAEhB,6BAA6B,CAAC;AAC/E;AAKA,MAAMP,yBAAkE,GAAG;EACzEwB,KAAKA,CAACvD,IAAI,EAAE;IACVA,IAAI,CAACwD,IAAI,CAAC,CAAC;EACb,CAAC;EACDC,gBAAgBA,CAACzD,IAAI,EAAE;IACrB,MAAM;MAAEc,eAAe;MAAED,QAAQ;MAAEJ;IAAS,CAAC,GAAG,IAAI;IAEpD,MAAM;MAAEiD;IAAG,CAAC,GAAG1D,IAAI,CAAC2D,IAAI;IACxB,IAAI,CAACD,EAAE,EAAE,MAAM,IAAIE,KAAK,CAAC,+BAA+B,CAAC;IACzD,MAAM1C,SAAS,GAAGwC,EAAE,CAACV,IAAI;IAEzB,MAAMa,WAAW,GAAGhD,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;IACjD,IAAI2C,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MAC1B,MAAMC,SAAS,GAAGlF,mBAAmB,CAEnCmF,sCAAsC,CACpCvD,QAAQ,EACRoD,WAAW,EACX9E,UAAU,CAACmC,SAAS,CAAC,EACrBlB,IAAI,CAAC6B,KACP,CACF,CAAC;MAEDkC,SAAS,CAACE,WAAW,GAAGjE,IAAI,CAAC2D,IAAI,CAACM,WAAW;MAE7CnD,eAAe,CAACd,IAAI,CAACkE,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;IACjD;EACF,CAAC;EACDI,mBAAmBA,CAACnE,IAAI,EAAE;IACxB,MAAM;MAAEc,eAAe;MAAED,QAAQ;MAAEJ;IAAS,CAAC,GAAG,IAAI;IAEpD,MAAM2D,KAAK,GAAGpE,IAAI,CAAC2D,IAAI,CAACU,IAAI,KAAK,KAAK;IAEtC,KAAK,MAAMC,IAAI,IAAItE,IAAI,CAACyB,GAAG,CAAC,cAAc,CAAC,EAAE;MAC3C,MAAM;QAAEiC;MAAG,CAAC,GAAGY,IAAI,CAACX,IAAI;MACxB,IAAI;QAAEY;MAAK,CAAC,GAAGD,IAAI,CAACX,IAAI;MACxB,IACExE,YAAY,CAACuE,EAAE,CAAC,IAChB7C,QAAQ,CAACwC,GAAG,CAACK,EAAE,CAACV,IAAI,CAAC,IACrB,CAAChE,yBAAyB,CAACuF,IAAI,CAAC,KAC/B,CAACrF,oBAAoB,CAACqF,IAAI,CAAC,IAAIA,IAAI,CAACb,EAAE,CAAC,KACvC,CAACzE,iBAAiB,CAACsF,IAAI,CAAC,IAAIA,IAAI,CAACb,EAAE,CAAC,EACrC;QACA,IAAI,CAACa,IAAI,EAAE;UACT,IAAIH,KAAK,EAAE;YAIT;UACF,CAAC,MAAM;YACLG,IAAI,GAAGvE,IAAI,CAAC6B,KAAK,CAAC2C,kBAAkB,CAAC,CAAC;UACxC;QACF;QAEAF,IAAI,CAACX,IAAI,CAACY,IAAI,GAAGP,sCAAsC,CACrDvD,QAAQ,EACRI,QAAQ,CAACY,GAAG,CAACiC,EAAE,CAACV,IAAI,CAAC,EACrBuB,IAAI,EACJvE,IAAI,CAAC6B,KACP,CAAC;QACDf,eAAe,CAACwD,IAAI,CAAC7C,GAAG,CAAC,MAAM,CAAC,CAAC;MACnC,CAAC,MAAM;QACL,KAAK,MAAMP,SAAS,IAAIuD,MAAM,CAACrC,IAAI,CACjCkC,IAAI,CAACxF,0BAA0B,CAAC,CAClC,CAAC,EAAE;UACD,IAAI+B,QAAQ,CAACwC,GAAG,CAACnC,SAAS,CAAC,EAAE;YAC3B,MAAM6C,SAAS,GAAGlF,mBAAmB,CAEnCmF,sCAAsC,CACpCvD,QAAQ,EACRI,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,EACvBnC,UAAU,CAACmC,SAAS,CAAC,EACrBlB,IAAI,CAAC6B,KACP,CACF,CAAC;YAEDkC,SAAS,CAACE,WAAW,GAAGjE,IAAI,CAAC2D,IAAI,CAACM,WAAW;YAE7CnD,eAAe,CAACd,IAAI,CAACkE,WAAW,CAACH,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;UACjD;QACF;MACF;IACF;EACF;AACF,CAAC;AAED,MAAMC,sCAAsC,GAAGA,CAC7CvD,QAAwB,EACxBoD,WAAqB,EACrBa,SAAuB,EACvB7C,KAAY,KACT;EACH,MAAM8C,iBAAiB,GAAGlE,QAAQ,CAACmE,UAAU;EAC7C,KACE,IAAIC,YAAY,GAAGhD,KAAK,EACxBgD,YAAY,IAAI,IAAI,EACpBA,YAAY,GAAGA,YAAY,CAAC5E,MAAM,EAClC;IACA,IAAI4E,YAAY,CAACC,aAAa,CAACH,iBAAiB,CAAC,EAAE;MACjDE,YAAY,CAACE,MAAM,CAACJ,iBAAiB,CAAC;IACxC;EACF;EACA,OAAO,CAACd,WAAW,IAAI,EAAE,EAAEmB,MAAM,CAAC,CAACC,IAAI,EAAEL,UAAU,KAAK;IAItD,MAAM;MAAExB;IAAiB,CAAC,GAAG3C,QAAQ;IACrC,MAAM0C,QAAQ,GAAGC,gBAAgB,CAACC,GAAG,CAACuB,UAAU,CAAC;IACjD,OAAOjG,oBAAoB,CACzB,GAAG,EACHa,gBAAgB,CACdT,UAAU,CAAC4F,iBAAiB,CAAC,EAC7BxB,QAAQ,GAAGxD,aAAa,CAACiF,UAAU,CAAC,GAAG7F,UAAU,CAAC6F,UAAU,CAAC,EAC9CzB,QACjB,CAAC,EACD8B,IACF,CAAC;EACH,CAAC,EAAEP,SAAS,CAAC;AACf,CAAC;AAED,MAAMQ,gBAAgB,GAAIhE,SAAiB,IAAK;EAC9C,OAAOiE,cAAQ,CAACC,UAAU,CAACC,GAAI;AACjC;AACA,+BAA+BnE,SAAU;AACzC;AACA,GAAG;AACH,CAAC;AAED,MAAMoC,wBAAgE,GAAG;EACvEgC,oBAAoBA,CAACtF,IAAI,EAAE;IACzB,MAAM;MAAEuC,IAAI;MAAEE,oBAAoB;MAAEZ,KAAK;MAAElB,QAAQ;MAAEG;IAAgB,CAAC,GACpE,IAAI;IACN,IAAIyB,IAAI,CAACc,GAAG,CAACrD,IAAI,CAAC2D,IAAI,CAAC,EAAE;IACzBpB,IAAI,CAACgD,GAAG,CAACvF,IAAI,CAAC2D,IAAI,CAAC;IAEnB,MAAMzC,SAAS,GAAGlB,IAAI,CAAC2D,IAAI,CAACX,IAAI;IAEhC,MAAMwC,UAAU,GAAG7E,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;IAC1C,IAAIsE,UAAU,EAAE;MACd,IAAIzF,QAAQ,CAACC,IAAI,CAAC,EAAE;QAClB,MAAMA,IAAI,CAACyF,mBAAmB,CAC3B,0CAAyCvE,SAAU,+CAA8C,GAC/F,qFACL,CAAC;MACH;MAEA,MAAMwE,YAAY,GAAG1F,IAAI,CAAC6B,KAAK,CAAC8D,UAAU,CAACzE,SAAS,CAAC;MACrD,MAAM0E,WAAW,GAAG/D,KAAK,CAAC8D,UAAU,CAACzE,SAAS,CAAC;MAG/C,IAAI0E,WAAW,KAAKF,YAAY,EAAE;MAElC,MAAMG,GAAG,GAAGpD,oBAAoB,CAAC+C,UAAU,EAAExF,IAAI,CAAC2D,IAAI,CAAC;MAGvDkC,GAAG,CAACC,GAAG,GAAG9F,IAAI,CAAC2D,IAAI,CAACmC,GAAG;MAEvB,IACE,CAAC9F,IAAI,CAACG,UAAU,CAAC4F,gBAAgB,CAAC;QAAEC,MAAM,EAAEhG,IAAI,CAAC2D;MAAK,CAAC,CAAC,IACtD3D,IAAI,CAACG,UAAU,CAAC8F,wBAAwB,CAAC;QAAED,MAAM,EAAEhG,IAAI,CAAC2D;MAAK,CAAC,CAAC,IAC/D3D,IAAI,CAACG,UAAU,CAAC+F,0BAA0B,CAAC;QAAEC,GAAG,EAAEnG,IAAI,CAAC2D;MAAK,CAAC,CAAC,KAChEvE,kBAAkB,CAACyG,GAAG,CAAC,EACvB;QACA7F,IAAI,CAACoG,WAAW,CAAC1G,kBAAkB,CAAC,CAACD,cAAc,CAAC,CAAC,CAAC,EAAEoG,GAAG,CAAC,CAAC,CAAC;MAChE,CAAC,MAAM,IAAI7F,IAAI,CAACqG,eAAe,CAAC,CAAC,IAAIjH,kBAAkB,CAACyG,GAAG,CAAC,EAAE;QAC5D,MAAM;UAAES,MAAM;UAAEC;QAAS,CAAC,GAAGV,GAAG;QAChC7F,IAAI,CAACoG,WAAW,CACd7G,mBAAmB,CAEjBD,aAAa,CAACgH,MAAM,CAACtD,IAAI,CAAC,EAE1B1D,aAAa,CAACiH,QAAQ,CAACvD,IAAI,CAC7B,CACF,CAAC;MACH,CAAC,MAAM;QACLhD,IAAI,CAACoG,WAAW,CAACP,GAAG,CAAC;MACvB;MAEA/E,eAAe,CAACd,IAAI,CAAC;MAIrBA,IAAI,CAACwD,IAAI,CAAC,CAAC;IACb;EACF,CAAC;EAEDgD,gBAAgBA,CAACxG,IAAI,EAAE;IACrB,MAAM;MACJ6B,KAAK;MACLU,IAAI;MACJ5B,QAAQ;MACRE,QAAQ;MACRC,eAAe;MACf2B;IACF,CAAC,GAAG,IAAI;IAER,IAAIF,IAAI,CAACc,GAAG,CAACrD,IAAI,CAAC2D,IAAI,CAAC,EAAE;IAEzBpB,IAAI,CAACgD,GAAG,CAACvF,IAAI,CAAC2D,IAAI,CAAC;IAEnB,MAAM8C,GAAG,GAAGzG,IAAI,CAACyB,GAAG,CAAC,UAAU,CAAC;IAGhC,IAAIgF,GAAG,CAACrH,kBAAkB,CAAC,CAAC,EAAE;IAE9B,MAAMsH,MAAM,GAAG1G,IAAI,CAAC2D,IAAI;IAExB,IAAI8C,GAAG,CAACtH,YAAY,CAAC,CAAC,EAAE;MACtB,MAAM+B,SAAS,GAAGuF,GAAG,CAAC9C,IAAI,CAACX,IAAI;MAG/B,IAAInB,KAAK,CAAC8D,UAAU,CAACzE,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAAC8D,UAAU,CAACzE,SAAS,CAAC,EAAE;QACpE;MACF;MAEA,MAAMyF,aAAa,GAAG9F,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;MAC7C,MAAMsE,UAAU,GAAG7E,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;MAE1C,IAAI,CAAAyF,aAAa,oBAAbA,aAAa,CAAE7C,MAAM,IAAG,CAAC,IAAI0B,UAAU,EAAE;QAC3C,IAAIA,UAAU,EAAE;UACdxF,IAAI,CAACoG,WAAW,CACdzH,oBAAoB,CAClB+H,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC,GAAG,GAAG,EACxBnE,oBAAoB,CAAC+C,UAAU,EAAEiB,GAAG,CAAC9C,IAAI,CAAC,EAC1CuB,gBAAgB,CAAChE,SAAS,CAC5B,CACF,CAAC;QACH,CAAC,MAAM,IAAIwF,MAAM,CAACG,MAAM,EAAE;UAGxB7G,IAAI,CAACoG,WAAW,CACdpC,sCAAsC,CACpC,IAAI,CAACvD,QAAQ,EACbkG,aAAa,EACb/H,SAAS,CAAC8H,MAAM,CAAC,EACjB1G,IAAI,CAAC6B,KACP,CACF,CAAC;QACH,CAAC,MAAM;UAGL,MAAMgE,GAAG,GAAGhE,KAAK,CAACiF,6BAA6B,CAAC5F,SAAS,CAAC;UAE1DlB,IAAI,CAACoG,WAAW,CACd1G,kBAAkB,CAAC,CACjBf,oBAAoB,CAAC,GAAG,EAAEC,SAAS,CAACiH,GAAG,CAAC,EAAEjH,SAAS,CAAC8H,MAAM,CAAC,CAAC,EAC5D1C,sCAAsC,CACpC,IAAI,CAACvD,QAAQ,EACbkG,aAAa,EACb5H,UAAU,CAACmC,SAAS,CAAC,EACrBlB,IAAI,CAAC6B,KACP,CAAC,EACDjD,SAAS,CAACiH,GAAG,CAAC,CACf,CACH,CAAC;QACH;MACF;IACF;IAEA/E,eAAe,CAACd,IAAI,CAAC;IACrBA,IAAI,CAACwD,IAAI,CAAC,CAAC;EACb,CAAC;EAEDuD,oBAAoB,EAAE;IACpBC,IAAIA,CAAChH,IAAI,EAAE;MACT,MAAM;QACJ6B,KAAK;QACLU,IAAI;QACJ5B,QAAQ;QACRE,QAAQ;QACRC,eAAe;QACf2B;MACF,CAAC,GAAG,IAAI;MAER,IAAIF,IAAI,CAACc,GAAG,CAACrD,IAAI,CAAC2D,IAAI,CAAC,EAAE;MACzBpB,IAAI,CAACgD,GAAG,CAACvF,IAAI,CAAC2D,IAAI,CAAC;MAEnB,MAAMsD,IAAI,GAAGjH,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC;MAG7B,IAAIwF,IAAI,CAAC7H,kBAAkB,CAAC,CAAC,EAAE;MAE/B,IAAI6H,IAAI,CAAC9H,YAAY,CAAC,CAAC,EAAE;QAGvB,MAAM+B,SAAS,GAAG+F,IAAI,CAACtD,IAAI,CAACX,IAAI;QAGhC,IAAInB,KAAK,CAAC8D,UAAU,CAACzE,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAAC8D,UAAU,CAACzE,SAAS,CAAC,EAAE;UACpE;QACF;QAEA,MAAMyF,aAAa,GAAG9F,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC;QAC7C,MAAMsE,UAAU,GAAG7E,QAAQ,CAACc,GAAG,CAACP,SAAS,CAAC;QAC1C,IAAI,CAAAyF,aAAa,oBAAbA,aAAa,CAAE7C,MAAM,IAAG,CAAC,IAAI0B,UAAU,EAAE;UAC3C0B,OAAM,CAAClH,IAAI,CAAC2D,IAAI,CAACiD,QAAQ,KAAK,GAAG,EAAE,yBAAyB,CAAC;UAE7D,MAAMO,UAAU,GAAGnH,IAAI,CAAC2D,IAAI;UAE5B,IAAI6B,UAAU,EAAE;YACd2B,UAAU,CAACF,IAAI,GAAGxE,oBAAoB,CAAC+C,UAAU,EAAEyB,IAAI,CAACtD,IAAI,CAAC;YAE7DwD,UAAU,CAACC,KAAK,GAAG1H,kBAAkB,CAAC,CACpCyH,UAAU,CAACC,KAAK,EAChBlC,gBAAgB,CAAChE,SAAS,CAAC,CAC5B,CAAC;UACJ;UAEAlB,IAAI,CAACoG,WAAW,CACdpC,sCAAsC,CACpC,IAAI,CAACvD,QAAQ,EACbkG,aAAa,EACbQ,UAAU,EACVnH,IAAI,CAAC6B,KACP,CACF,CAAC;UACDf,eAAe,CAACd,IAAI,CAAC;QACvB;MACF,CAAC,MAAM;QACL,MAAMqH,GAAG,GAAGJ,IAAI,CAACnI,0BAA0B,CAAC,CAAC;QAC7C,MAAMwI,eAAe,GAAG7C,MAAM,CAACrC,IAAI,CAACiF,GAAG,CAAC,CAACE,MAAM,CAC7CrG,SAAS,IACPW,KAAK,CAAC8D,UAAU,CAACzE,SAAS,CAAC,KAAKlB,IAAI,CAAC6B,KAAK,CAAC8D,UAAU,CAACzE,SAAS,CACnE,CAAC;QACD,MAAMwC,EAAE,GAAG4D,eAAe,CAACE,IAAI,CAACtG,SAAS,IAAIP,QAAQ,CAAC0C,GAAG,CAACnC,SAAS,CAAC,CAAC;QAErE,IAAIwC,EAAE,EAAE;UACN1D,IAAI,CAAC2D,IAAI,CAACyD,KAAK,GAAG1H,kBAAkB,CAAC,CACnCM,IAAI,CAAC2D,IAAI,CAACyD,KAAK,EACflC,gBAAgB,CAACxB,EAAE,CAAC,CACrB,CAAC;QACJ;QAIA,MAAM+D,KAAqB,GAAG,EAAE;QAChCH,eAAe,CAACI,OAAO,CAACxG,SAAS,IAAI;UACnC,MAAMyF,aAAa,GAAG9F,QAAQ,CAACY,GAAG,CAACP,SAAS,CAAC,IAAI,EAAE;UACnD,IAAIyF,aAAa,CAAC7C,MAAM,GAAG,CAAC,EAAE;YAC5B2D,KAAK,CAAC/F,IAAI,CACRsC,sCAAsC,CACpC,IAAI,CAACvD,QAAQ,EACbkG,aAAa,EACb5H,UAAU,CAACmC,SAAS,CAAC,EACrBlB,IAAI,CAAC6B,KACP,CACF,CAAC;UACH;QACF,CAAC,CAAC;QAEF,IAAI4F,KAAK,CAAC3D,MAAM,GAAG,CAAC,EAAE;UACpB,IAAIH,IAAY,GAAGjE,kBAAkB,CAAC+H,KAAK,CAAC;UAC5C,IAAIzH,IAAI,CAACG,UAAU,CAACwH,qBAAqB,CAAC,CAAC,EAAE;YAC3ChE,IAAI,GAAG9E,mBAAmB,CAAC8E,IAAI,CAAC;YAEhCA,IAAI,CAACM,WAAW,GAAGjE,IAAI,CAACG,UAAU,CAACwD,IAAI,CAACM,WAAW;UACrD;UAEA,MAAMF,SAAS,GAAG/D,IAAI,CAACkE,WAAW,CAACP,IAAI,CAAC,CAAC,CAAC,CAAC;UAC3C7C,eAAe,CAACiD,SAAS,CAAC;QAC5B;MACF;IACF;EACF,CAAC;EACD,+BAA+B6D,CAC7B5H,IAAmD,EACnD;IACA,MAAM;MAAE6B,KAAK;MAAE8B;IAAK,CAAC,GAAG3D,IAAI;IAC5B,MAAM;MAAEiH;IAAK,CAAC,GAAGtD,IAAI;IACrB,MAAM;MAAE9C,QAAQ;MAAEF,QAAQ;MAAEkB,KAAK,EAAEgG;IAAa,CAAC,GAAG,IAAI;IAExD,IAAI,CAACxI,qBAAqB,CAAC4H,IAAI,CAAC,EAAE;MAChC,IAAIa,kBAAkB,GAAG,KAAK;QAC5BC,wBAAwB;MAC1B,MAAMC,aAAa,GAAGhI,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC,CAACI,KAAK;MAC5C,KAAK,MAAMmB,IAAI,IAAIyB,MAAM,CAACrC,IAAI,CAACtD,0BAA0B,CAACmI,IAAI,CAAC,CAAC,EAAE;QAChE,IAAIY,YAAY,CAAClC,UAAU,CAAC3C,IAAI,CAAC,KAAKnB,KAAK,CAAC8D,UAAU,CAAC3C,IAAI,CAAC,EAAE;UAC5D,IAAInC,QAAQ,CAACwC,GAAG,CAACL,IAAI,CAAC,EAAE;YACtB8E,kBAAkB,GAAG,IAAI;YACzB,IAAIE,aAAa,CAAClD,aAAa,CAAC9B,IAAI,CAAC,EAAE;cACrCgF,aAAa,CAACjD,MAAM,CAAC/B,IAAI,CAAC;YAC5B;UACF;UACA,IAAIrC,QAAQ,CAAC0C,GAAG,CAACL,IAAI,CAAC,IAAI,CAAC+E,wBAAwB,EAAE;YACnDA,wBAAwB,GAAG/E,IAAI;UACjC;QACF;MACF;MACA,IAAI,CAAC8E,kBAAkB,IAAI,CAACC,wBAAwB,EAAE;QACpD;MACF;MAEA/H,IAAI,CAACiI,WAAW,CAAC,CAAC;MAClB,MAAMC,QAAQ,GAAGlI,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC;MAEjC,MAAM0G,SAAS,GAAGtG,KAAK,CAACuG,gCAAgC,CAACnB,IAAI,CAAC;MAC9DjH,IAAI,CACDyB,GAAG,CAAC,MAAM,CAAC,CACX2E,WAAW,CACVxG,mBAAmB,CAAC,KAAK,EAAE,CACzBC,kBAAkB,CAACjB,SAAS,CAACuJ,SAAS,CAAC,CAAC,CACzC,CACH,CAAC;MACHtG,KAAK,CAACwG,mBAAmB,CAACrI,IAAI,CAACyB,GAAG,CAAC,MAAM,CAAC,CAAC;MAE3C,IAAIqG,kBAAkB,EAAE;QACtBI,QAAQ,CAACI,gBAAgB,CACvB,MAAM,EACNzJ,mBAAmB,CAACF,oBAAoB,CAAC,GAAG,EAAEsI,IAAI,EAAEkB,SAAS,CAAC,CAChE,CAAC;MACH;MACA,IAAIJ,wBAAwB,EAAE;QAC5BG,QAAQ,CAACI,gBAAgB,CACvB,MAAM,EACNzJ,mBAAmB,CAACqG,gBAAgB,CAAC6C,wBAAwB,CAAC,CAChE,CAAC;MACH;IACF;EACF;AACF,CAAC"}