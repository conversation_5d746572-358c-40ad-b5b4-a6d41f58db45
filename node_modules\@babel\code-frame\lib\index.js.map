{"version": 3, "names": ["_highlight", "require", "_picocolors", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "colors", "process", "env", "FORCE_COLOR", "createColors", "_colors", "compose", "f", "g", "v", "pcWithForcedColor", "undefined", "getColors", "forceColor", "_pcWithForcedColor", "deprecationWarningShown", "getDefs", "gutter", "gray", "marker", "red", "bold", "message", "NEWLINE", "getMarkerLines", "loc", "source", "opts", "startLoc", "assign", "column", "line", "start", "endLoc", "end", "linesAbove", "linesBelow", "startLine", "startColumn", "endLine", "endColumn", "Math", "max", "min", "length", "lineDiff", "markerLines", "lineNumber", "sourceLength", "codeFrameColumns", "rawLines", "highlighted", "highlightCode", "should<PERSON><PERSON><PERSON>", "defs", "maybe<PERSON><PERSON><PERSON>", "fmt", "string", "lines", "split", "hasColumns", "numberMaxWidth", "String", "highlightedLines", "highlight", "frame", "slice", "map", "index", "number", "paddedNumber", "<PERSON><PERSON><PERSON><PERSON>", "lastMarkerLine", "markerLine", "Array", "isArray", "markerSpacing", "replace", "numberOfMarkers", "repeat", "join", "reset", "_default", "colNumber", "emitWarning", "deprecationError", "Error", "name", "console", "warn", "location"], "sources": ["../src/index.ts"], "sourcesContent": ["import highlight, { shouldHighlight } from \"@babel/highlight\";\n\nimport _colors, { createColors } from \"picocolors\";\nimport type { Colors, Formatter } from \"picocolors/types\";\n// See https://github.com/alexeyraspopov/picocolors/issues/62\nconst colors =\n  typeof process === \"object\" &&\n  (process.env.FORCE_COLOR === \"0\" || process.env.FORCE_COLOR === \"false\")\n    ? createColors(false)\n    : _colors;\n\nconst compose: <T, U, V>(f: (gv: U) => V, g: (v: T) => U) => (v: T) => V =\n  (f, g) => v =>\n    f(g(v));\n\nlet pcWithForcedColor: Colors = undefined;\nfunction getColors(forceColor: boolean) {\n  if (forceColor) {\n    pcWithForcedColor ??= createColors(true);\n    return pcWithForcedColor;\n  }\n  return colors;\n}\n\nlet deprecationWarningShown = false;\n\ntype Location = {\n  column: number;\n  line: number;\n};\n\ntype NodeLocation = {\n  end?: Location;\n  start: Location;\n};\n\nexport interface Options {\n  /** Syntax highlight the code as JavaScript for terminals. default: false */\n  highlightCode?: boolean;\n  /**  The number of lines to show above the error. default: 2 */\n  linesAbove?: number;\n  /**  The number of lines to show below the error. default: 3 */\n  linesBelow?: number;\n  /**\n   * Forcibly syntax highlight the code as JavaScript (for non-terminals);\n   * overrides highlightCode.\n   * default: false\n   */\n  forceColor?: boolean;\n  /**\n   * Pass in a string to be displayed inline (if possible) next to the\n   * highlighted location in the code. If it can't be positioned inline,\n   * it will be placed above the code frame.\n   * default: nothing\n   */\n  message?: string;\n}\n\n/**\n * Styles for code frame token types.\n */\nfunction getDefs(colors: Colors) {\n  return {\n    gutter: colors.gray,\n    marker: compose(colors.red, colors.bold),\n    message: compose(colors.red, colors.bold),\n  };\n}\n\n/**\n * RegExp to test for newlines in terminal.\n */\n\nconst NEWLINE = /\\r\\n|[\\n\\r\\u2028\\u2029]/;\n\n/**\n * Extract what lines should be marked and highlighted.\n */\n\ntype MarkerLines = Record<number, true | [number, number]>;\n\nfunction getMarkerLines(\n  loc: NodeLocation,\n  source: Array<string>,\n  opts: Options,\n): {\n  start: number;\n  end: number;\n  markerLines: MarkerLines;\n} {\n  const startLoc: Location = {\n    column: 0,\n    line: -1,\n    ...loc.start,\n  };\n  const endLoc: Location = {\n    ...startLoc,\n    ...loc.end,\n  };\n  const { linesAbove = 2, linesBelow = 3 } = opts || {};\n  const startLine = startLoc.line;\n  const startColumn = startLoc.column;\n  const endLine = endLoc.line;\n  const endColumn = endLoc.column;\n\n  let start = Math.max(startLine - (linesAbove + 1), 0);\n  let end = Math.min(source.length, endLine + linesBelow);\n\n  if (startLine === -1) {\n    start = 0;\n  }\n\n  if (endLine === -1) {\n    end = source.length;\n  }\n\n  const lineDiff = endLine - startLine;\n  const markerLines: MarkerLines = {};\n\n  if (lineDiff) {\n    for (let i = 0; i <= lineDiff; i++) {\n      const lineNumber = i + startLine;\n\n      if (!startColumn) {\n        markerLines[lineNumber] = true;\n      } else if (i === 0) {\n        const sourceLength = source[lineNumber - 1].length;\n\n        markerLines[lineNumber] = [startColumn, sourceLength - startColumn + 1];\n      } else if (i === lineDiff) {\n        markerLines[lineNumber] = [0, endColumn];\n      } else {\n        const sourceLength = source[lineNumber - i].length;\n\n        markerLines[lineNumber] = [0, sourceLength];\n      }\n    }\n  } else {\n    if (startColumn === endColumn) {\n      if (startColumn) {\n        markerLines[startLine] = [startColumn, 0];\n      } else {\n        markerLines[startLine] = true;\n      }\n    } else {\n      markerLines[startLine] = [startColumn, endColumn - startColumn];\n    }\n  }\n\n  return { start, end, markerLines };\n}\n\nexport function codeFrameColumns(\n  rawLines: string,\n  loc: NodeLocation,\n  opts: Options = {},\n): string {\n  const highlighted =\n    (opts.highlightCode || opts.forceColor) && shouldHighlight(opts);\n  const colors = getColors(opts.forceColor);\n  const defs = getDefs(colors);\n  const maybeHighlight = (fmt: Formatter, string: string) => {\n    return highlighted ? fmt(string) : string;\n  };\n  const lines = rawLines.split(NEWLINE);\n  const { start, end, markerLines } = getMarkerLines(loc, lines, opts);\n  const hasColumns = loc.start && typeof loc.start.column === \"number\";\n\n  const numberMaxWidth = String(end).length;\n\n  const highlightedLines = highlighted ? highlight(rawLines, opts) : rawLines;\n\n  let frame = highlightedLines\n    .split(NEWLINE, end)\n    .slice(start, end)\n    .map((line, index) => {\n      const number = start + 1 + index;\n      const paddedNumber = ` ${number}`.slice(-numberMaxWidth);\n      const gutter = ` ${paddedNumber} |`;\n      const hasMarker = markerLines[number];\n      const lastMarkerLine = !markerLines[number + 1];\n      if (hasMarker) {\n        let markerLine = \"\";\n        if (Array.isArray(hasMarker)) {\n          const markerSpacing = line\n            .slice(0, Math.max(hasMarker[0] - 1, 0))\n            .replace(/[^\\t]/g, \" \");\n          const numberOfMarkers = hasMarker[1] || 1;\n\n          markerLine = [\n            \"\\n \",\n            maybeHighlight(defs.gutter, gutter.replace(/\\d/g, \" \")),\n            \" \",\n            markerSpacing,\n            maybeHighlight(defs.marker, \"^\").repeat(numberOfMarkers),\n          ].join(\"\");\n\n          if (lastMarkerLine && opts.message) {\n            markerLine += \" \" + maybeHighlight(defs.message, opts.message);\n          }\n        }\n        return [\n          maybeHighlight(defs.marker, \">\"),\n          maybeHighlight(defs.gutter, gutter),\n          line.length > 0 ? ` ${line}` : \"\",\n          markerLine,\n        ].join(\"\");\n      } else {\n        return ` ${maybeHighlight(defs.gutter, gutter)}${\n          line.length > 0 ? ` ${line}` : \"\"\n        }`;\n      }\n    })\n    .join(\"\\n\");\n\n  if (opts.message && !hasColumns) {\n    frame = `${\" \".repeat(numberMaxWidth + 1)}${opts.message}\\n${frame}`;\n  }\n\n  if (highlighted) {\n    return colors.reset(frame);\n  } else {\n    return frame;\n  }\n}\n\n/**\n * Create a code frame, adding line numbers, code highlighting, and pointing to a given position.\n */\n\nexport default function (\n  rawLines: string,\n  lineNumber: number,\n  colNumber?: number | null,\n  opts: Options = {},\n): string {\n  if (!deprecationWarningShown) {\n    deprecationWarningShown = true;\n\n    const message =\n      \"Passing lineNumber and colNumber is deprecated to @babel/code-frame. Please use `codeFrameColumns`.\";\n\n    if (process.emitWarning) {\n      // A string is directly supplied to emitWarning, because when supplying an\n      // Error object node throws in the tests because of different contexts\n      process.emitWarning(message, \"DeprecationWarning\");\n    } else {\n      const deprecationError = new Error(message);\n      deprecationError.name = \"DeprecationWarning\";\n      console.warn(new Error(message));\n    }\n  }\n\n  colNumber = Math.max(colNumber, 0);\n\n  const location: NodeLocation = {\n    start: { column: colNumber, line: lineNumber },\n  };\n\n  return codeFrameColumns(rawLines, location, opts);\n}\n"], "mappings": ";;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,WAAA,GAAAC,uBAAA,CAAAF,OAAA;AAAmD,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAGnD,MAAMY,MAAM,GACV,OAAOC,OAAO,KAAK,QAAQ,KAC1BA,OAAO,CAACC,GAAG,CAACC,WAAW,KAAK,GAAG,IAAIF,OAAO,CAACC,GAAG,CAACC,WAAW,KAAK,OAAO,CAAC,GACpE,IAAAC,wBAAY,EAAC,KAAK,CAAC,GACnBC,mBAAO;AAEb,MAAMC,OAAkE,GACtEA,CAACC,CAAC,EAAEC,CAAC,KAAKC,CAAC,IACTF,CAAC,CAACC,CAAC,CAACC,CAAC,CAAC,CAAC;AAEX,IAAIC,iBAAyB,GAAGC,SAAS;AACzC,SAASC,SAASA,CAACC,UAAmB,EAAE;EACtC,IAAIA,UAAU,EAAE;IAAA,IAAAC,kBAAA;IACd,CAAAA,kBAAA,GAAAJ,iBAAiB,YAAAI,kBAAA,GAAjBJ,iBAAiB,GAAK,IAAAN,wBAAY,EAAC,IAAI,CAAC;IACxC,OAAOM,iBAAiB;EAC1B;EACA,OAAOV,MAAM;AACf;AAEA,IAAIe,uBAAuB,GAAG,KAAK;AAqCnC,SAASC,OAAOA,CAAChB,MAAc,EAAE;EAC/B,OAAO;IACLiB,MAAM,EAAEjB,MAAM,CAACkB,IAAI;IACnBC,MAAM,EAAEb,OAAO,CAACN,MAAM,CAACoB,GAAG,EAAEpB,MAAM,CAACqB,IAAI,CAAC;IACxCC,OAAO,EAAEhB,OAAO,CAACN,MAAM,CAACoB,GAAG,EAAEpB,MAAM,CAACqB,IAAI;EAC1C,CAAC;AACH;AAMA,MAAME,OAAO,GAAG,yBAAyB;AAQzC,SAASC,cAAcA,CACrBC,GAAiB,EACjBC,MAAqB,EACrBC,IAAa,EAKb;EACA,MAAMC,QAAkB,GAAArC,MAAA,CAAAsC,MAAA;IACtBC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,CAAC;EAAC,GACLN,GAAG,CAACO,KAAK,CACb;EACD,MAAMC,MAAgB,GAAA1C,MAAA,CAAAsC,MAAA,KACjBD,QAAQ,EACRH,GAAG,CAACS,GAAG,CACX;EACD,MAAM;IAAEC,UAAU,GAAG,CAAC;IAAEC,UAAU,GAAG;EAAE,CAAC,GAAGT,IAAI,IAAI,CAAC,CAAC;EACrD,MAAMU,SAAS,GAAGT,QAAQ,CAACG,IAAI;EAC/B,MAAMO,WAAW,GAAGV,QAAQ,CAACE,MAAM;EACnC,MAAMS,OAAO,GAAGN,MAAM,CAACF,IAAI;EAC3B,MAAMS,SAAS,GAAGP,MAAM,CAACH,MAAM;EAE/B,IAAIE,KAAK,GAAGS,IAAI,CAACC,GAAG,CAACL,SAAS,IAAIF,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;EACrD,IAAID,GAAG,GAAGO,IAAI,CAACE,GAAG,CAACjB,MAAM,CAACkB,MAAM,EAAEL,OAAO,GAAGH,UAAU,CAAC;EAEvD,IAAIC,SAAS,KAAK,CAAC,CAAC,EAAE;IACpBL,KAAK,GAAG,CAAC;EACX;EAEA,IAAIO,OAAO,KAAK,CAAC,CAAC,EAAE;IAClBL,GAAG,GAAGR,MAAM,CAACkB,MAAM;EACrB;EAEA,MAAMC,QAAQ,GAAGN,OAAO,GAAGF,SAAS;EACpC,MAAMS,WAAwB,GAAG,CAAC,CAAC;EAEnC,IAAID,QAAQ,EAAE;IACZ,KAAK,IAAI/C,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI+C,QAAQ,EAAE/C,CAAC,EAAE,EAAE;MAClC,MAAMiD,UAAU,GAAGjD,CAAC,GAAGuC,SAAS;MAEhC,IAAI,CAACC,WAAW,EAAE;QAChBQ,WAAW,CAACC,UAAU,CAAC,GAAG,IAAI;MAChC,CAAC,MAAM,IAAIjD,CAAC,KAAK,CAAC,EAAE;QAClB,MAAMkD,YAAY,GAAGtB,MAAM,CAACqB,UAAU,GAAG,CAAC,CAAC,CAACH,MAAM;QAElDE,WAAW,CAACC,UAAU,CAAC,GAAG,CAACT,WAAW,EAAEU,YAAY,GAAGV,WAAW,GAAG,CAAC,CAAC;MACzE,CAAC,MAAM,IAAIxC,CAAC,KAAK+C,QAAQ,EAAE;QACzBC,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAEP,SAAS,CAAC;MAC1C,CAAC,MAAM;QACL,MAAMQ,YAAY,GAAGtB,MAAM,CAACqB,UAAU,GAAGjD,CAAC,CAAC,CAAC8C,MAAM;QAElDE,WAAW,CAACC,UAAU,CAAC,GAAG,CAAC,CAAC,EAAEC,YAAY,CAAC;MAC7C;IACF;EACF,CAAC,MAAM;IACL,IAAIV,WAAW,KAAKE,SAAS,EAAE;MAC7B,IAAIF,WAAW,EAAE;QACfQ,WAAW,CAACT,SAAS,CAAC,GAAG,CAACC,WAAW,EAAE,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLQ,WAAW,CAACT,SAAS,CAAC,GAAG,IAAI;MAC/B;IACF,CAAC,MAAM;MACLS,WAAW,CAACT,SAAS,CAAC,GAAG,CAACC,WAAW,EAAEE,SAAS,GAAGF,WAAW,CAAC;IACjE;EACF;EAEA,OAAO;IAAEN,KAAK;IAAEE,GAAG;IAAEY;EAAY,CAAC;AACpC;AAEO,SAASG,gBAAgBA,CAC9BC,QAAgB,EAChBzB,GAAiB,EACjBE,IAAa,GAAG,CAAC,CAAC,EACV;EACR,MAAMwB,WAAW,GACf,CAACxB,IAAI,CAACyB,aAAa,IAAIzB,IAAI,CAACd,UAAU,KAAK,IAAAwC,0BAAe,EAAC1B,IAAI,CAAC;EAClE,MAAM3B,MAAM,GAAGY,SAAS,CAACe,IAAI,CAACd,UAAU,CAAC;EACzC,MAAMyC,IAAI,GAAGtC,OAAO,CAAChB,MAAM,CAAC;EAC5B,MAAMuD,cAAc,GAAGA,CAACC,GAAc,EAAEC,MAAc,KAAK;IACzD,OAAON,WAAW,GAAGK,GAAG,CAACC,MAAM,CAAC,GAAGA,MAAM;EAC3C,CAAC;EACD,MAAMC,KAAK,GAAGR,QAAQ,CAACS,KAAK,CAACpC,OAAO,CAAC;EACrC,MAAM;IAAES,KAAK;IAAEE,GAAG;IAAEY;EAAY,CAAC,GAAGtB,cAAc,CAACC,GAAG,EAAEiC,KAAK,EAAE/B,IAAI,CAAC;EACpE,MAAMiC,UAAU,GAAGnC,GAAG,CAACO,KAAK,IAAI,OAAOP,GAAG,CAACO,KAAK,CAACF,MAAM,KAAK,QAAQ;EAEpE,MAAM+B,cAAc,GAAGC,MAAM,CAAC5B,GAAG,CAAC,CAACU,MAAM;EAEzC,MAAMmB,gBAAgB,GAAGZ,WAAW,GAAG,IAAAa,kBAAS,EAACd,QAAQ,EAAEvB,IAAI,CAAC,GAAGuB,QAAQ;EAE3E,IAAIe,KAAK,GAAGF,gBAAgB,CACzBJ,KAAK,CAACpC,OAAO,EAAEW,GAAG,CAAC,CACnBgC,KAAK,CAAClC,KAAK,EAAEE,GAAG,CAAC,CACjBiC,GAAG,CAAC,CAACpC,IAAI,EAAEqC,KAAK,KAAK;IACpB,MAAMC,MAAM,GAAGrC,KAAK,GAAG,CAAC,GAAGoC,KAAK;IAChC,MAAME,YAAY,GAAI,IAAGD,MAAO,EAAC,CAACH,KAAK,CAAC,CAACL,cAAc,CAAC;IACxD,MAAM5C,MAAM,GAAI,IAAGqD,YAAa,IAAG;IACnC,MAAMC,SAAS,GAAGzB,WAAW,CAACuB,MAAM,CAAC;IACrC,MAAMG,cAAc,GAAG,CAAC1B,WAAW,CAACuB,MAAM,GAAG,CAAC,CAAC;IAC/C,IAAIE,SAAS,EAAE;MACb,IAAIE,UAAU,GAAG,EAAE;MACnB,IAAIC,KAAK,CAACC,OAAO,CAACJ,SAAS,CAAC,EAAE;QAC5B,MAAMK,aAAa,GAAG7C,IAAI,CACvBmC,KAAK,CAAC,CAAC,EAAEzB,IAAI,CAACC,GAAG,CAAC6B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,CACvCM,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC;QACzB,MAAMC,eAAe,GAAGP,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC;QAEzCE,UAAU,GAAG,CACX,KAAK,EACLlB,cAAc,CAACD,IAAI,CAACrC,MAAM,EAAEA,MAAM,CAAC4D,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,EACvD,GAAG,EACHD,aAAa,EACbrB,cAAc,CAACD,IAAI,CAACnC,MAAM,EAAE,GAAG,CAAC,CAAC4D,MAAM,CAACD,eAAe,CAAC,CACzD,CAACE,IAAI,CAAC,EAAE,CAAC;QAEV,IAAIR,cAAc,IAAI7C,IAAI,CAACL,OAAO,EAAE;UAClCmD,UAAU,IAAI,GAAG,GAAGlB,cAAc,CAACD,IAAI,CAAChC,OAAO,EAAEK,IAAI,CAACL,OAAO,CAAC;QAChE;MACF;MACA,OAAO,CACLiC,cAAc,CAACD,IAAI,CAACnC,MAAM,EAAE,GAAG,CAAC,EAChCoC,cAAc,CAACD,IAAI,CAACrC,MAAM,EAAEA,MAAM,CAAC,EACnCc,IAAI,CAACa,MAAM,GAAG,CAAC,GAAI,IAAGb,IAAK,EAAC,GAAG,EAAE,EACjC0C,UAAU,CACX,CAACO,IAAI,CAAC,EAAE,CAAC;IACZ,CAAC,MAAM;MACL,OAAQ,IAAGzB,cAAc,CAACD,IAAI,CAACrC,MAAM,EAAEA,MAAM,CAAE,GAC7Cc,IAAI,CAACa,MAAM,GAAG,CAAC,GAAI,IAAGb,IAAK,EAAC,GAAG,EAChC,EAAC;IACJ;EACF,CAAC,CAAC,CACDiD,IAAI,CAAC,IAAI,CAAC;EAEb,IAAIrD,IAAI,CAACL,OAAO,IAAI,CAACsC,UAAU,EAAE;IAC/BK,KAAK,GAAI,GAAE,GAAG,CAACc,MAAM,CAAClB,cAAc,GAAG,CAAC,CAAE,GAAElC,IAAI,CAACL,OAAQ,KAAI2C,KAAM,EAAC;EACtE;EAEA,IAAId,WAAW,EAAE;IACf,OAAOnD,MAAM,CAACiF,KAAK,CAAChB,KAAK,CAAC;EAC5B,CAAC,MAAM;IACL,OAAOA,KAAK;EACd;AACF;AAMe,SAAAiB,SACbhC,QAAgB,EAChBH,UAAkB,EAClBoC,SAAyB,EACzBxD,IAAa,GAAG,CAAC,CAAC,EACV;EACR,IAAI,CAACZ,uBAAuB,EAAE;IAC5BA,uBAAuB,GAAG,IAAI;IAE9B,MAAMO,OAAO,GACX,qGAAqG;IAEvG,IAAIrB,OAAO,CAACmF,WAAW,EAAE;MAGvBnF,OAAO,CAACmF,WAAW,CAAC9D,OAAO,EAAE,oBAAoB,CAAC;IACpD,CAAC,MAAM;MACL,MAAM+D,gBAAgB,GAAG,IAAIC,KAAK,CAAChE,OAAO,CAAC;MAC3C+D,gBAAgB,CAACE,IAAI,GAAG,oBAAoB;MAC5CC,OAAO,CAACC,IAAI,CAAC,IAAIH,KAAK,CAAChE,OAAO,CAAC,CAAC;IAClC;EACF;EAEA6D,SAAS,GAAG1C,IAAI,CAACC,GAAG,CAACyC,SAAS,EAAE,CAAC,CAAC;EAElC,MAAMO,QAAsB,GAAG;IAC7B1D,KAAK,EAAE;MAAEF,MAAM,EAAEqD,SAAS;MAAEpD,IAAI,EAAEgB;IAAW;EAC/C,CAAC;EAED,OAAOE,gBAAgB,CAACC,QAAQ,EAAEwC,QAAQ,EAAE/D,IAAI,CAAC;AACnD", "ignoreList": []}