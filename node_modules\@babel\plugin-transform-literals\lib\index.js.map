{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "assertVersion", "name", "visitor", "NumericLiteral", "node", "extra", "test", "raw", "undefined", "StringLiteral"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  return {\n    name: \"transform-literals\",\n\n    visitor: {\n      NumericLiteral({ node }) {\n        // number octal like 0b10 or 0o70\n        // @ts-expect-error Add node.extra typings\n        if (node.extra && /^0[ob]/i.test(node.extra.raw)) {\n          node.extra = undefined;\n        }\n      },\n\n      StringLiteral({ node }) {\n        // unicode escape\n        // @ts-expect-error Add node.extra typings\n        if (node.extra && /\\\\[u]/gi.test(node.extra.raw)) {\n          node.extra = undefined;\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EAEtC,OAAO;IACLC,IAAI,EAAE,oBAAoB;IAE1BC,OAAO,EAAE;MACPC,cAAcA,CAAC;QAAEC;MAAK,CAAC,EAAE;QAGvB,IAAIA,IAAI,CAACC,KAAK,IAAI,SAAS,CAACC,IAAI,CAACF,IAAI,CAACC,KAAK,CAACE,GAAG,CAAC,EAAE;UAChDH,IAAI,CAACC,KAAK,GAAGG,SAAS;QACxB;MACF,CAAC;MAEDC,aAAaA,CAAC;QAAEL;MAAK,CAAC,EAAE;QAGtB,IAAIA,IAAI,CAACC,KAAK,IAAI,SAAS,CAACC,IAAI,CAACF,IAAI,CAACC,KAAK,CAACE,GAAG,CAAC,EAAE;UAChDH,IAAI,CAACC,KAAK,GAAGG,SAAS;QACxB;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}