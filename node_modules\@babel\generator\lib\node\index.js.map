{"version": 3, "names": ["whitespace", "require", "parens", "_t", "FLIPPED_ALIAS_KEYS", "isCallExpression", "isExpressionStatement", "isMemberExpression", "isNewExpression", "expandAliases", "obj", "map", "Map", "add", "type", "func", "fn", "get", "set", "node", "parent", "stack", "_fn", "Object", "keys", "aliases", "alias", "expandedParens", "expandedWhitespaceNodes", "nodes", "isOrHasCallExpression", "object", "needsWhitespace", "_expandedWhitespaceNo", "expression", "flag", "needsWhitespaceBefore", "needsWhitespaceAfter", "needsParens", "printStack", "_expandedParens$get", "callee"], "sources": ["../../src/node/index.ts"], "sourcesContent": ["import * as whitespace from \"./whitespace.ts\";\nimport * as parens from \"./parentheses.ts\";\nimport {\n  FLIPPED_ALIAS_KEYS,\n  isCallExpression,\n  isExpressionStatement,\n  isMemberExpression,\n  isNewExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nimport type { WhitespaceFlag } from \"./whitespace.ts\";\n\ntype NodeHandler<R> = (\n  node: t.Node,\n  // todo:\n  // node: K extends keyof typeof t\n  //   ? Extract<typeof t[K], { type: \"string\" }>\n  //   : t.Node,\n  parent: t.Node,\n  stack?: t.Node[],\n) => R;\n\nexport type NodeHandlers<R> = {\n  [K in string]?: NodeHandler<R>;\n};\n\nfunction expandAliases<R>(obj: NodeHandlers<R>) {\n  const map = new Map<string, NodeHandler<R>>();\n\n  function add(type: string, func: NodeHandler<R>) {\n    const fn = map.get(type);\n    map.set(\n      type,\n      fn\n        ? function (node, parent, stack) {\n            return fn(node, parent, stack) ?? func(node, parent, stack);\n          }\n        : func,\n    );\n  }\n\n  for (const type of Object.keys(obj)) {\n    const aliases = FLIPPED_ALIAS_KEYS[type];\n    if (aliases) {\n      for (const alias of aliases) {\n        add(alias, obj[type]);\n      }\n    } else {\n      add(type, obj[type]);\n    }\n  }\n\n  return map;\n}\n\n// Rather than using `t.is` on each object property, we pre-expand any type aliases\n// into concrete types so that the 'find' call below can be as fast as possible.\nconst expandedParens = expandAliases(parens);\nconst expandedWhitespaceNodes = expandAliases(whitespace.nodes);\n\nfunction isOrHasCallExpression(node: t.Node): boolean {\n  if (isCallExpression(node)) {\n    return true;\n  }\n\n  return isMemberExpression(node) && isOrHasCallExpression(node.object);\n}\n\nexport function needsWhitespace(\n  node: t.Node,\n  parent: t.Node,\n  type: WhitespaceFlag,\n): boolean {\n  if (!node) return false;\n\n  if (isExpressionStatement(node)) {\n    node = node.expression;\n  }\n\n  const flag = expandedWhitespaceNodes.get(node.type)?.(node, parent);\n\n  if (typeof flag === \"number\") {\n    return (flag & type) !== 0;\n  }\n\n  return false;\n}\n\nexport function needsWhitespaceBefore(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 1);\n}\n\nexport function needsWhitespaceAfter(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 2);\n}\n\nexport function needsParens(\n  node: t.Node,\n  parent: t.Node,\n  printStack?: t.Node[],\n) {\n  if (!parent) return false;\n\n  if (isNewExpression(parent) && parent.callee === node) {\n    if (isOrHasCallExpression(node)) return true;\n  }\n\n  return expandedParens.get(node.type)?.(node, parent, printStack);\n}\n"], "mappings": ";;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,EAAA,GAAAF,OAAA;AAMsB;EALpBG,kBAAkB;EAClBC,gBAAgB;EAChBC,qBAAqB;EACrBC,kBAAkB;EAClBC;AAAe,IAAAL,EAAA;AAoBjB,SAASM,aAAaA,CAAIC,GAAoB,EAAE;EAC9C,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAyB,CAAC;EAE7C,SAASC,GAAGA,CAACC,IAAY,EAAEC,IAAoB,EAAE;IAC/C,MAAMC,EAAE,GAAGL,GAAG,CAACM,GAAG,CAACH,IAAI,CAAC;IACxBH,GAAG,CAACO,GAAG,CACLJ,IAAI,EACJE,EAAE,GACE,UAAUG,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAE;MAAA,IAAAC,GAAA;MAC7B,QAAAA,GAAA,GAAON,EAAE,CAACG,IAAI,EAAEC,MAAM,EAAEC,KAAK,CAAC,YAAAC,GAAA,GAAIP,IAAI,CAACI,IAAI,EAAEC,MAAM,EAAEC,KAAK,CAAC;IAC7D,CAAC,GACDN,IACN,CAAC;EACH;EAEA,KAAK,MAAMD,IAAI,IAAIS,MAAM,CAACC,IAAI,CAACd,GAAG,CAAC,EAAE;IACnC,MAAMe,OAAO,GAAGrB,kBAAkB,CAACU,IAAI,CAAC;IACxC,IAAIW,OAAO,EAAE;MACX,KAAK,MAAMC,KAAK,IAAID,OAAO,EAAE;QAC3BZ,GAAG,CAACa,KAAK,EAAEhB,GAAG,CAACI,IAAI,CAAC,CAAC;MACvB;IACF,CAAC,MAAM;MACLD,GAAG,CAACC,IAAI,EAAEJ,GAAG,CAACI,IAAI,CAAC,CAAC;IACtB;EACF;EAEA,OAAOH,GAAG;AACZ;AAIA,MAAMgB,cAAc,GAAGlB,aAAa,CAACP,MAAM,CAAC;AAC5C,MAAM0B,uBAAuB,GAAGnB,aAAa,CAACT,UAAU,CAAC6B,KAAK,CAAC;AAE/D,SAASC,qBAAqBA,CAACX,IAAY,EAAW;EACpD,IAAId,gBAAgB,CAACc,IAAI,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;EAEA,OAAOZ,kBAAkB,CAACY,IAAI,CAAC,IAAIW,qBAAqB,CAACX,IAAI,CAACY,MAAM,CAAC;AACvE;AAEO,SAASC,eAAeA,CAC7Bb,IAAY,EACZC,MAAc,EACdN,IAAoB,EACX;EAAA,IAAAmB,qBAAA;EACT,IAAI,CAACd,IAAI,EAAE,OAAO,KAAK;EAEvB,IAAIb,qBAAqB,CAACa,IAAI,CAAC,EAAE;IAC/BA,IAAI,GAAGA,IAAI,CAACe,UAAU;EACxB;EAEA,MAAMC,IAAI,IAAAF,qBAAA,GAAGL,uBAAuB,CAACX,GAAG,CAACE,IAAI,CAACL,IAAI,CAAC,qBAAtCmB,qBAAA,CAAyCd,IAAI,EAAEC,MAAM,CAAC;EAEnE,IAAI,OAAOe,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO,CAACA,IAAI,GAAGrB,IAAI,MAAM,CAAC;EAC5B;EAEA,OAAO,KAAK;AACd;AAEO,SAASsB,qBAAqBA,CAACjB,IAAY,EAAEC,MAAc,EAAE;EAClE,OAAOY,eAAe,CAACb,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC;AACzC;AAEO,SAASiB,oBAAoBA,CAAClB,IAAY,EAAEC,MAAc,EAAE;EACjE,OAAOY,eAAe,CAACb,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC;AACzC;AAEO,SAASkB,WAAWA,CACzBnB,IAAY,EACZC,MAAc,EACdmB,UAAqB,EACrB;EAAA,IAAAC,mBAAA;EACA,IAAI,CAACpB,MAAM,EAAE,OAAO,KAAK;EAEzB,IAAIZ,eAAe,CAACY,MAAM,CAAC,IAAIA,MAAM,CAACqB,MAAM,KAAKtB,IAAI,EAAE;IACrD,IAAIW,qBAAqB,CAACX,IAAI,CAAC,EAAE,OAAO,IAAI;EAC9C;EAEA,QAAAqB,mBAAA,GAAOb,cAAc,CAACV,GAAG,CAACE,IAAI,CAACL,IAAI,CAAC,qBAA7B0B,mBAAA,CAAgCrB,IAAI,EAAEC,MAAM,EAAEmB,UAAU,CAAC;AAClE", "ignoreList": []}